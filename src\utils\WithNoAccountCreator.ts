import { NextApiResponse } from "next";
import { InitialInterestedCreator } from "../analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import { NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";

/**
 * @deprecated
 */
export default async function withNoAccountCreator(
  req: NextApiRequestWithSession,
  res: NextApiResponse
): Promise<InitialInterestedCreator | null> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());

  const interestedCreator = (req.session.noAccountCreator as InitialInterestedCreator) || null;

  return Promise.resolve(interestedCreator);
}
