import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationRejectedPagePropsController from "./controllers/ApplicationRejectedPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import ContentManagementService from "@src/api/services/ContentManagementService";

const applicationRejectedPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationRejectedPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient)
    )
  );

export default applicationRejectedPageProps;
