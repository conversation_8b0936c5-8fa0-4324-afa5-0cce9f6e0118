import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import TermsAndConditionsPagePropsController from "@src/serverprops/controllers/TermsAndConditionsPagePropsController";
import config from "config";

export type TermsAndConditionsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  opportunityId: string;
  user: AuthenticatedUser;
  initialPage: string;
  locale: string;
  urlLocale: string;
  FLAG_COUNTRIES_BY_TYPE: boolean;
};

const termsAndConditionsProps = (locale: string) =>
  serverPropsControllerFactory(
    new TermsAndConditionsPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default termsAndConditionsProps;
