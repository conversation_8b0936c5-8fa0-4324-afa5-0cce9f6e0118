import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import ProfilePagePropsController from "./controllers/ProfilePagePropsController";
import { OAuthError } from "@src/controllers/ConnectTikTokAccountController";
import config from "config";

export type ProfilePageProps = {
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
  CN_LAUNCH_DATE: string;
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  error: OAuthError | null;
  pages: { id: string; name: string; accessToken: string }[];
  invalidTikTokScope: boolean;
  FLAG_COUNTRIES_BY_TYPE: boolean;
};

const profileProps = (locale: string) =>
  serverPropsControllerFactory(
    new ProfilePagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default profileProps;
