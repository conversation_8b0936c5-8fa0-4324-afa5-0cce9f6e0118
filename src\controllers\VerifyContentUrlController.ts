import { Inject, Service } from "typedi";
import { NextApiRequest, NextApiResponse } from "next";
import ContentScanningHttpClient, { ContentUrls, ScanType } from "../contentScanning/ContentScanningHttpClient";
import { Controller, RequestHandler, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class VerifyContentUrlController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly contentScanning: ContentScanningHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    const { urls }: { urls: ContentUrls } = req.body;
    const type = req.query?.type as ScanType;
    const { data } = await this.contentScanning.verifyUrls(urls, type);
    this.json(res, data);
  }

  async handleMarkUploadComplete(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    const { contentId } = req.body;
    await this.contentScanning.markUploadComplete(contentId);
    this.empty(res, HttpStatus.OK);
  }
}

export default VerifyContentUrlController;
