import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export type CommunicationPreferencesProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
};

export default class CommunicationPreferencesPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<CommunicationPreferencesProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<CommunicationPreferencesProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "breadcrumb",
          "communication-preferences",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
