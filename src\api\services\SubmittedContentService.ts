import { ContentStatus } from "@eait-playerexp-cn/core-ui-kit";
import { AxiosResponse } from "axios";
import { SubmittedContentFeedbackCriteria } from "@src/submittedContent/ContentFeedbackHttpClient";
import {
  ContentsWithDeliverable,
  ContentsWithReviewFinalRemark,
  WebsiteContent
} from "@src/submittedContent/SubmittedContentHttpClient";
import client from "./Client";
import { PreSignedUrl, PreSignedUrlV2 } from "@src/submittedContent/SignedUrlsHttpClient";
import ContentFeedback from "@src/submittedContent/ContentFeedback";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export type ContentDetail = {
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  contentType: string;
};

export type SubmittedWebsiteContent = {
  participationId: string;
  websiteContent: {
    contentDetails: ContentDetail[];
  };
};

export type UpdatedWebsiteContent = {
  participationId: string;
  websiteContent: {
    id: string;
    contentDetails: ContentDetail;
  };
};

export type SubmittedSocialMediaContent = {
  participationId: string;
  socialMediaContent: {
    creatorId: string;
    contentUrl: string;
    accountId: string;
  };
};

export type SubmittedUploadContent = {
  fileName: string;
  title: string;
  versionId: string;
  thumbnail: string;
  id: string;
  contentType: string;
  participationId: string;
};
export type SubmittedUploadContentDeliverable = SubmittedUploadContent & {
  deliverableId: string;
};

export type SubmittedSocialMediaContentDeliverable = SubmittedSocialMediaContent & {
  socialMediaContent: {
    deliverableId: string;
  };
};

export type SubmittedWebsiteContentDeliverable = {
  participationId: string;
  websiteContent: WebsiteContent & {
    deliverableId: string;
  };
};

export type SubmittedContentWithoutDeliverablesPage = {
  count: number;
  total: number;
  contents: SubmittedContent[];
};

export type SubmittedContentWithDeliverablesPage = {
  count: number;
  total: number;
  contents: SubmittedContentWithDeliverableDetail[];
};

export type SubmittedContentWithReviewFinalRemarkPage = {
  count: number;
  total: number;
  contents: SubmittedContentWithFinalRemark[];
};

export type SignedURLRequestBody = {
  participationId: string;
  creatorId: string;
  deliverableId: string;
  fileName: string;
  title: string;
  extension: string;
  contentType: string;
  mimeType: string;
  thumbnail: string;
  fileSize: number;
  programCode: string;
};
export const CONTENT_SCAN_SOURCE_TYPE_FILE_UPLOAD = "FILE_UPLOAD";

export type SignedURLRequestBodyV2 = Omit<SignedURLRequestBody, "title"> & {
  nucleusId: string;
  contentTitle: string;
  contentDescription: string;
  contentScanSourceType: string;
};

export class SubmittedContent {
  readonly submittedDate: LocalizedDate;
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly thumbnail: string;
  readonly status: ContentStatus;
  readonly contentUri: string;
  readonly contentType: string;
  readonly contentTypeLabel: string;
  readonly opportunityName: string;
  readonly opportunityId: string;
  readonly sourceType: string;
  readonly submittedOn: number;

  constructor(content) {
    Object.assign(this, content);
    this.submittedDate = new LocalizedDate(content.submittedDate);
    this.contentType = content.contentType === "carousel_album" ? "image" : content.contentType;
  }

  public formattedSubmittedDate(locale: string): string {
    return `${this.submittedDate.formatWithoutTime(undefined, locale)} (${LocalizedDate.timeZoneAbbreviation()})`;
  }

  public requiresChanges(): boolean {
    return this.status === "CHANGE_REQUESTED";
  }
}

export class SubmittedContentWithDeliverableDetail extends SubmittedContent {
  readonly deliverableId: string;

  constructor(content: ContentsWithDeliverable) {
    super(content);
    this.deliverableId = content.deliverableId;
  }
}

export class SubmittedContentWithFinalRemark extends SubmittedContentWithDeliverableDetail {
  readonly reviewFinalRemark: {
    content: string;
    author: string;
    date: LocalizedDate;
  };

  constructor(content: ContentsWithReviewFinalRemark) {
    super(content);
    this.reviewFinalRemark = content.reviewFinalRemark
      ? {
          content: content.reviewFinalRemark?.content,
          author: content.reviewFinalRemark?.author,
          date: content.reviewFinalRemark?.date ? new LocalizedDate(content.reviewFinalRemark.date) : null
        }
      : null;
  }

  public formattedReviewFinalRemarkDate(locale: string): string {
    return this.reviewFinalRemark?.date
      ? `${this.reviewFinalRemark?.date?.formatWithCalendarLabels(
          "MMM DD, YYYY",
          locale
        )} (${LocalizedDate.timeZoneAbbreviation()})`
      : null;
  }
}

export class ContentsFeedback {
  private readonly lastUpdateDate: LocalizedDate;

  constructor(content) {
    Object.assign(this, content);
    this.lastUpdateDate = new LocalizedDate(content.lastUpdateDate);
  }

  public formattedSubmittedDate(locale: string): string {
    return `${this.lastUpdateDate.formatWithCalendarLabels(
      "MMM DD, YYYY",
      locale
    )} (${LocalizedDate.timeZoneAbbreviation()})`;
  }
}

const validateContent = async (url: { urls: string[] }, type: string): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/verify-content-url", { body: url, query: { type } })) as AxiosResponse<void>;
};

const submitWebsiteContent = async (submittedWebsiteContent: SubmittedWebsiteContent): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/submitted-website-content", {
    body: submittedWebsiteContent
  })) as AxiosResponse<void>;
};

const updateWebsiteContent = async (updatedWebsiteContent: UpdatedWebsiteContent): Promise<AxiosResponse<void>> => {
  return (await client.put("/api/submitted-website-content", { body: updatedWebsiteContent })) as AxiosResponse<void>;
};

const getSignedUrl = async (
  participationId: string,
  fileName: string,
  fileId?: string
): Promise<AxiosResponse<PreSignedUrl>> => {
  return (await client.get("/api/signed-urls", {
    query: { participationId, fileName, fileId }
  })) as AxiosResponse<PreSignedUrl>;
};

const getSignedUrlV1 = async (signedURLRequestBody: SignedURLRequestBody): Promise<AxiosResponse<PreSignedUrl>> => {
  return (await client.post("/api/signed-urls", { body: signedURLRequestBody })) as AxiosResponse<PreSignedUrl>;
};

const getSignedUrlV2 = async (
  signedURLRequestBodyV2: SignedURLRequestBodyV2
): Promise<AxiosResponse<PreSignedUrlV2>> => {
  return (await client.post("/api/v2/signed-urls", { body: signedURLRequestBodyV2 })) as AxiosResponse<PreSignedUrlV2>;
};

const updateUploadedContent = async (updateUploadedContent: SubmittedUploadContent): Promise<AxiosResponse<void>> => {
  return (await client.put("/api/uploaded-contents", { body: updateUploadedContent })) as AxiosResponse<void>;
};

const getContentsFeedback = async (
  criteria: SubmittedContentFeedbackCriteria
): Promise<AxiosResponse<ContentFeedback[]>> => {
  return client.get("/api/submitted-content-feedback", { query: criteria }).then((response: AxiosResponse) => {
    response.data.contentsFeedback = response.data.contentsFeedback?.map(
      (content: ContentFeedback) => new ContentsFeedback(content)
    );
    return response;
  });
};

const submitSocialMediaContents = async (
  submittedSocialMediaContentDeliverable: SubmittedSocialMediaContentDeliverable
): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/content-submission/v4/submitted-social-media-content", {
    body: submittedSocialMediaContentDeliverable
  })) as AxiosResponse<void>;
};

const submitSocialMediaContentWithInstagramMeadia = async (
  submittedSocialMediaContentDeliverable: SubmittedSocialMediaContentDeliverable
): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/content-submission/v5/submitted-social-media-content", {
    body: submittedSocialMediaContentDeliverable
  })) as AxiosResponse<void>;
};

const submitWebsiteContentDeliverable = async (
  submittedWebsiteContentDeliverable: SubmittedWebsiteContentDeliverable
): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/v2/submitted-website-content", {
    body: submittedWebsiteContentDeliverable
  })) as AxiosResponse<void>;
};

const submitUploadedContentDeliverable = async (
  submittedUploadContentDeliverable: SubmittedUploadContentDeliverable
): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/v2/uploaded-contents", {
    body: submittedUploadContentDeliverable
  })) as AxiosResponse<void>;
};

const markUploadComplete = async (contentId: string): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/v2/uploaded-contents", { body: { contentId } })) as AxiosResponse<void>;
};

const getSubmittedContents = async (
  size: number,
  page: number,
  participationId: string,
  deliverableId?: string
): Promise<AxiosResponse<SubmittedContentWithDeliverablesPage>> => {
  return client
    .get("/api/v4/submitted-content", { query: { limit: size, page, participationId, deliverableId } })
    .then((response: AxiosResponse) => {
      response.data.contents = response.data.contents?.map(
        (content: ContentsWithDeliverable) => new SubmittedContentWithDeliverableDetail(content)
      );
      return response;
    });
};

const getSubmittedContentsFinalRemarks = async (
  size: number,
  page: number,
  participationId: string,
  deliverableId?: string
): Promise<AxiosResponse<SubmittedContentWithReviewFinalRemarkPage>> => {
  return client
    .get("/api/v5/submitted-content", { query: { limit: size, page, participationId, deliverableId } })
    .then((response: AxiosResponse) => {
      response.data.contents = response.data.contents?.map(
        (content: ContentsWithReviewFinalRemark) => new SubmittedContentWithFinalRemark(content)
      );
      return response;
    });
};

const getSubmittedContentsFinalRemarksWithProgramCode = async (
  size: number,
  page: number,
  participationId: string,
  deliverableId?: string
): Promise<AxiosResponse<SubmittedContentWithReviewFinalRemarkPage>> => {
  return client
    .get("/api/v6/submitted-content", { query: { size, page, participationId, deliverableId } })
    .then((response: AxiosResponse) => {
      response.data.contents = response.data.contents?.map(
        (content: ContentsWithReviewFinalRemark) => new SubmittedContentWithFinalRemark(content)
      );
      return response;
    });
};

const SubmittedContentService = {
  validateContent,
  submitWebsiteContent,
  getSignedUrl,
  getSignedUrlV1,
  getSignedUrlV2,
  getContentsFeedback,
  updateWebsiteContent,
  updateUploadedContent,
  submitSocialMediaContents,
  submitSocialMediaContentWithInstagramMeadia,
  submitWebsiteContentDeliverable,
  submitUploadedContentDeliverable,
  getSubmittedContents,
  markUploadComplete,
  getSubmittedContentsFinalRemarks,
  getSubmittedContentsFinalRemarksWithProgramCode
};

export default SubmittedContentService;
