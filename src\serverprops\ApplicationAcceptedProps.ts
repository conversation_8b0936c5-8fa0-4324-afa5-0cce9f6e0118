import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationAcceptedPagePropsController from "./controllers/ApplicationAcceptedPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import ContentManagementService from "@src/api/services/ContentManagementService";

const applicationAcceptedProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationAcceptedPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient)
    )
  );

export default applicationAcceptedProps;
