import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import OpportunitiesHttpClient, { SearchOpportunityWithProgram } from "../opportunities/OpportunitiesHttpClient";
import SearchOpportunityWithPerksCriteria from "../opportunities/SearchOpportunityWithPerksCriteria";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "config";

@Service()
class SearchOpportunityWithEventDetailsController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly opportunities: OpportunitiesHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    let criteria: SearchOpportunityWithPerksCriteria = req.body;
    let searchedOpportunity;
    if (config.FLAG_OPPORTUNITIES_PER_PROGRAM) {
      criteria = { ...req.body, program: config.PROGRAM_CODE };
      searchedOpportunity = await this.opportunities.searchOpportunitiesWithProgram(
        user.id as string,
        criteria as SearchOpportunityWithProgram
      );
    } else {
      searchedOpportunity = await this.opportunities.searchOpportunitiesWithEventDetails(
        user.id as string,
        criteria as SearchOpportunityWithPerksCriteria
      );
    }
    this.json(res, searchedOpportunity);
  }
}

export default SearchOpportunityWithEventDetailsController;
