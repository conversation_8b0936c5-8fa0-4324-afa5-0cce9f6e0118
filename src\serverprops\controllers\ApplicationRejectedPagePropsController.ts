import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { ApplicationRejectedPageLabels } from "@src/contentManagement/ApplicationRejectedPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { ApplicationRejectedProps } from "pages/interested-creators/application-rejected";
import featureFlags from "utils/feature-flags";

export default class ApplicationRejectedPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ApplicationRejectedProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<ApplicationRejectedProps>> {
    const nucleusId = this.identity(req).nucleusId;
    const application = await this.getApplication(nucleusId);
    if (!featureFlags.isInterestedCreatorFlowEnabled() || !application?.isRejected()) return { notFound: true };

    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : false;

    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "applicationRejected"
    )) as ApplicationRejectedPageLabels & CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        showInitialMessage,
        locale: this.currentLocale,
        pageLabels,
        application: Object.assign({}, application),
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled()
      }
    };
  }

  private async getApplication(nucleusId: number): Promise<InterestedCreatorApplicationStatus> {
    let existingApplication;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplication = await this.applications.forCreatorWithProgram(
        nucleusId as unknown as number,
        config.PROGRAM_CODE
      );
    } else if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      existingApplication = await this.applications.forCreatorWithApplicationStatus(nucleusId as unknown as number);
    } else {
      existingApplication = await this.applications.forCreatorWith(nucleusId as unknown as number);
    }
    if (existingApplication) {
      existingApplication.createdDate = this.currentLocale
        ? existingApplication.createdDateformattedWithoutTime(this.currentLocale)
        : existingApplication.createdDate;

      if (existingApplication.canResubmitRequestDate) {
        existingApplication.canResubmitRequestDate = existingApplication.formatResubmitRequestDateWithoutTime(
          this.currentLocale
        );
      }
    }

    return existingApplication;
  }
}
