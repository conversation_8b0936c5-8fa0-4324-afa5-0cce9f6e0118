import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type NoAccountLabels = {
  noAccountLabels: {
    title: string;
    applyNow: string;
    exploreTitle: string;
    creatorNetwork: string;
    availablePerks: string;
    subTitlePart1: string;
    subTitlePart2: string;
    descriptionPara1: string;
    descriptionPara2: string;
    descriptionPara3: string;
    howItWorks: string;
    requestToJoin: string;
  };
};

export class NoAccountPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): NoAccountLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      noAccountLabels: {
        title: microCopy.get("noAccount.title"),
        applyNow: microCopy.get("noAccount.applyNow"),
        exploreTitle: microCopy.get("noAccount.exploreTitle"),
        creatorNetwork: microCopy.get("noAccount.creatorNetwork"),
        availablePerks: microCopy.get("noAccount.availablePerks"),
        subTitlePart1: microCopy.get("noAccount.subTitlePart1"),
        subTitlePart2: microCopy.get("noAccount.subTitlePart2"),
        descriptionPara1: microCopy.get("noAccount.descriptionPara1"),
        descriptionPara2: microCopy.get("noAccount.descriptionPara2"),
        descriptionPara3: microCopy.get("noAccount.descriptionPara3"),
        howItWorks: microCopy.get("noAccount.howItWorks"),
        requestToJoin: microCopy.get("common.requestToJoin")
      }
    };
  }
}
