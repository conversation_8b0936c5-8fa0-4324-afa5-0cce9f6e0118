import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import InformationPagePropsController from "./controllers/InformationPagePropsController";
import config from "config";

const informationProps = (locale: string) =>
  serverPropsControllerFactory(
    new InformationPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default informationProps;
