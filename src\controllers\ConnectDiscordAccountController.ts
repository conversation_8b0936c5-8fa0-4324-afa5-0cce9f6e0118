import { NextApiResponse } from "next";
import ConnectedAccountCredentials from "../channels/ConnectedAccountCredentials";
import { Inject, Service } from "typedi";
import DiscordAccountHttpClient from "../channels/DiscordAccountHttpClient";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "../../config";

@Service()
class ConnectDiscordAccountController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly discordAccount: DiscordAccountHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const code = this.query(req, "code") as string;
    const creator = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    const credentials: ConnectedAccountCredentials = ConnectedAccountCredentials.forCreator(creator.id, code);
    if (this.query(req, "error") === "access_denied") {
      this.html(res, "<script>window.close();</script>");
      return;
    }
    await this.discordAccount.connectDiscordAccount(credentials);

    this.html(res, "<script>window.close();</script>");
  }
}

export default ConnectDiscordAccountController;
