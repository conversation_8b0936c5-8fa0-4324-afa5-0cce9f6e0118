import "reflect-metadata";
import { NextApiResponse } from "next";
import { InterestedCreator } from "../api/services/InterestedCreatorsServices";
import config from "../../config";
import ApiContainer from "../ApiContainer";
import InterestedCreatorApplicationsHttpClient from "../interestedCreators/InterestedCreatorApplicationsHttpClient";
import { interestedCreatorPages } from "../../pages/interested-creators/information";
import { addTelemetryInformation, NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";

/**
 * @deprecated
 */
export default async function withInterestedCreator(
  req: NextApiRequestWithSession,
  res: NextApiResponse,
  page?: string,
  locale?: string
): Promise<InterestedCreator | null> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());
  await addTelemetryInformation(req, res, () => Promise.resolve());
  let interestedCreator = (req.session.interestedCreator as InterestedCreator) || null;
  if (!config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
    return Promise.resolve(interestedCreator);
  }

  const applications = ApiContainer.get(InterestedCreatorApplicationsHttpClient);
  let existingApplicantInformation;
  if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
    existingApplicantInformation = await applications.forCreatorWithProgram(
      interestedCreator?.nucleusId,
      config.PROGRAM_CODE
    );
  } else {
    existingApplicantInformation = await applications.forCreatorWithApplicationStatus(interestedCreator?.nucleusId);
  }
  if (!existingApplicantInformation) {
    return Promise.resolve(interestedCreator);
  }

  if (page === interestedCreatorPages.information) {
    interestedCreator = { ...existingApplicantInformation.applicantInformation, ...interestedCreator };
    return Promise.resolve(interestedCreator);
  }

  if (page === interestedCreatorPages.creatorTypes) {
    if (
      (interestedCreator.creatorTypes?.length == 0 || interestedCreator.creatorTypes === undefined) &&
      existingApplicantInformation
    ) {
      interestedCreator.creatorTypes = existingApplicantInformation.applicantInformation?.creatorTypes;
    }
    return Promise.resolve(interestedCreator);
  }

  if (page === interestedCreatorPages.franchises) {
    interestedCreator = {
      ...interestedCreator,
      preferredFranchises: [
        ...interestedCreator.preferredFranchises,
        ...existingApplicantInformation.applicantInformation?.preferredFranchises
      ]
    };
    return Promise.resolve(interestedCreator);
  }

  interestedCreator = {
    ...interestedCreator,
    createdDate: existingApplicantInformation.createdDateformattedWithoutTime(locale)
  };

  return Promise.resolve(interestedCreator);
}
