import { useRouter } from "next/router";
import React, { FC, memo, ReactNode, useEffect, useState } from "react";
import Loading from "./Loading";
import Header from "./migrations/Header";
import {
  communicationPreferences,
  connectAccounts,
  creatorType,
  franchisesYouPlay,
  information,
  SvgProps,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../src/context";
import { ONBOARDING_STEPS } from "../utils";
import Layout, { LayoutBody, LayoutHeader } from "./Layout";

export type NavStep = {
  icon: FC<SvgProps>;
  title: string;
  href: string;
  isCompleted?: boolean;
};

export const LayoutContext = React.createContext({});

export type Labels = {
  back: string;
  title: string;
  close: string;
};

export type MigrationLabelsType = {
  information: string;
  franchisesYouPlay: string;
  creatorType: string;
  connectAccounts?: string;
  communicationPreferences?: string;
  termsAndConditions?: string;
  contract?: string;
  paymentInfo?: string;
  creatorCode?: string;
};

export type MigrationLayoutProps = {
  children?: ReactNode;
  onClose: () => void;
  showHeader?: boolean;
  isLoading?: boolean;
  labels: Labels;
  pageTitle?: string;
  className?: string;
  showLogo?: boolean;
  isRegistrationFlow?: boolean;
  migration?: MigrationLabelsType;
  isOnboardingFlow?: boolean;
  stableDispatch?: (action) => void;
  onGoBack?: () => void;
  setShowMigration?: () => void;
  setNavigateToPage?: () => void;
  completed?: string;
};

export default memo(function MigrationLayout({
  children,
  onClose,
  showHeader = true,
  isLoading = false,
  labels,
  pageTitle,
  migration,
  className = "",
  showLogo = true,
  isRegistrationFlow,
  stableDispatch,
  isOnboardingFlow = false,
  onGoBack,
  setShowMigration,
  setNavigateToPage,
  completed
}: MigrationLayoutProps) {
  const router = useRouter();
  const {
    state: { onboardingSteps }
  } = useAppContext();
  const [steps, setSteps] = useState([]);
  const [currentPage, setCurrentPage] = useState({});

  useEffect(() => {
    if (isOnboardingFlow) {
      const steps: Array<NavStep> = isRegistrationFlow
        ? [
            {
              icon: information,
              title: migration?.information,
              href: "/onboarding/information",
              isCompleted: false
            },
            {
              icon: franchisesYouPlay,
              title: migration?.franchisesYouPlay,
              href: "/franchises-you-play",
              isCompleted: false
            },
            {
              icon: creatorType,
              title: migration?.creatorType,
              href: "/creator-type",
              isCompleted: false
            },
            {
              icon: connectAccounts,
              title: migration?.connectAccounts,
              href: "/connect-accounts",
              isCompleted: false
            },
            {
              icon: communicationPreferences,
              title: migration?.communicationPreferences,
              href: "/communication-preferences",
              isCompleted: false
            },
            {
              icon: termsAndConditions,
              title: migration?.termsAndConditions,
              href: "/terms-and-conditions",
              isCompleted: false
            }
          ]
        : [
            {
              icon: information,
              title: migration?.information,
              href: "/interested-creators/information",
              isCompleted: false
            },
            {
              icon: creatorType,
              title: migration?.creatorType,
              href: "/interested-creators/creator-types",
              isCompleted: false
            },
            {
              icon: franchisesYouPlay,
              title: migration?.franchisesYouPlay,
              href: "/interested-creators/franchises-you-play",
              isCompleted: false
            }
          ];
      setSteps(steps);
      if (onboardingSteps.length === 0) stableDispatch({ type: ONBOARDING_STEPS, data: steps });
    }
  }, [isRegistrationFlow, isOnboardingFlow]);

  useEffect(() => {
    if (isOnboardingFlow) setSteps(onboardingSteps);
  }, [isOnboardingFlow, onboardingSteps]);

  useEffect(() => {
    if (isOnboardingFlow) {
      const currentPage = steps.find(({ href }) => href === router.pathname) || null;
      setCurrentPage(currentPage);
    }
  }, [isOnboardingFlow, steps]);

  return (
    <Layout>
      {showLogo && <LayoutHeader pageTitle={pageTitle} />}
      <LayoutBody>
        {isLoading && <Loading />}
        <LayoutContext.Provider value={currentPage}>
          <div className={`mg-container ${className}`}>
            {showHeader && (
              <Header
                {...{
                  onClose,
                  steps,
                  currentPage,
                  labels,
                  stableDispatch,
                  onGoBack,
                  setShowMigration,
                  setNavigateToPage,
                  completedLabel: completed
                }}
              />
            )}
            <div className="mg-bg"> </div>
            {<div className="mg-page">{children}</div>}
          </div>
        </LayoutContext.Provider>
      </LayoutBody>
    </Layout>
  );
});
