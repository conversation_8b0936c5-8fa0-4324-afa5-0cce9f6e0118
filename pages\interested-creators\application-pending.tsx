import "reflect-metadata";
import React, { ComponentType } from "react";
import flags from "../../utils/feature-flags";
import withCreatorApplication from "../../src/utils/WithCreatorApplication";
import BrowserAnalytics from "../../src/analytics/BrowserAnalytics";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import InterestedCreatorApplication from "../../src/interestedCreators/InterestedCreatorApplication";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import config from "../../config";
import InterestedCreatorApplicationStatus from "../../src/interestedCreators/InterestedCreatorApplicationStatus";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import Layout, { LayoutBody } from "../../components/Layout";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationPendingPageProps from "@src/serverprops/ApplicationPendingPageProps";
import Loading from "@components/Loading";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { ApplicationPendingPageLabels } from "@src/contentManagement/ApplicationPendingPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

const ApplicationPendingPage: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/ApplicationPendingPage"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type ApplicationPendingProps = {
  runtimeConfiguration?: Record<string, unknown>;
  application: InterestedCreatorApplicationStatus;
  locale: string;
  analytics?: BrowserAnalytics;
  showInitialMessage?: boolean;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  RE_APPLY_THRESHOLD_IN_DAYS?: number;
  pageLabels: ApplicationPendingPageLabels & CommonPageLabels;
};

export default function Pending({
  application,
  locale,
  analytics = new BrowserAnalytics(),
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  pageLabels
}: ApplicationPendingProps): JSX.Element {
  const { applicationPendingLabels, commonPageLabels } = pageLabels;
  const { creatorNetwork, close } = commonPageLabels;
  const { configuration } = useDependency();
  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <div className="mg-bg"> </div>
          <ApplicationPendingPage
            labels={applicationPendingLabels}
            emailId={application?.email}
            locale={locale}
            analytics={analytics}
            canApply={configuration.FLAG_INTERESTED_CREATOR_CAN_APPLY && application?.canApply}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={application.createdDate as unknown as string}
            redirectedToMain="/api/logout"
            redirectedToInformationForm="/interested-creators/information"
            applicationPendingThumbnail={"/img/home-header--980w-x-690h.png"}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

type ApplicationPendingPage = {
  locale: string;
  application: InterestedCreatorApplication;
};

export const getServerSideProps: GetServerSideProps<ApplicationPendingPage> = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationPendingPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<ApplicationPendingProps>;
  }

  const application = await withCreatorApplication(req, res, locale);
  if (!flags.isInterestedCreatorFlowEnabled() || !application?.isPending()) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "applicationPending");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      application: Object.assign({}, application),
      pageLabels,
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled(),
      RE_APPLY_THRESHOLD_IN_DAYS: config.RE_APPLY_THRESHOLD_IN_DAYS,
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
