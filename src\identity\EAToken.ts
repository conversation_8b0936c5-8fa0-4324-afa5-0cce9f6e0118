import Token from "../authentication/Token";
import Identity from "../authentication/Identity";

/**
 * @deprecated
 */
export default class EAToken implements Token {
  private creatorIdentity: Identity;

  constructor(private authorizationCode: string) {}

  identity(): Identity {
    return this.creatorIdentity;
  }

  setIdentity(identity: Identity): void {
    this.creatorIdentity = identity;
  }

  code(): string {
    return this.authorizationCode;
  }
}
