import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import PaymentInformationPagePropsController from "./controllers/PaymentInformationPagePropsController";
import config from "config";

const paymentInformationProps = (locale: string) =>
  serverPropsControllerFactory(
    new PaymentInformationPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default paymentInformationProps;
