import { NextApiResponse } from "next";
import InterestedCreator from "../interestedCreators/InterestedCreator";
import { NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";

/**
 * @deprecated
 */
export default async function withDeactivatedAccount(
  req: NextApiRequestWithSession,
  res: NextApiResponse
): Promise<InterestedCreator | null> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());

  const deactivatedAccount = (req.session.deactivatedAccount as InterestedCreator) || null;

  return Promise.resolve(deactivatedAccount);
}
