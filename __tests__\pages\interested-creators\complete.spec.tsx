import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";
import ApplicationComplete from "../../../pages/interested-creators/complete";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationCompletePageLabels } from "@src/contentManagement/ApplicationCompletePageMapper";

jest.mock("next/router");

// Mock the dynamic component with more detailed props tracking
const mockApplicationCompletedPage = jest.fn();
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = (props: any) => {
    mockApplicationCompletedPage(props);
    return <div data-testid="application-completed-page" {...props} />;
  };
  DynamicComponent.displayName = "ApplicationCompletedPage";
  return DynamicComponent;
});

describe("InterestedCreatorsComplete", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const testInterestedCreator = {
    ...anInitialInterestedCreator(),
    nucleusId: 12345,
    contentLanguages: [{ code: "en", name: "English" }],
    createdDate: "2023-12-12T10:00:00Z",
    originEmail: "<EMAIL>"
  };

  const basePageLabels = {
    applicationCompletePageLabels: {
      title: "Application Complete",
      description: "Thank you for your submission",
      buttonText: "Back to Home"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationCompletePageLabels & CommonPageLabels;

  const applicationCompleteProps = {
    locale,
    interestedCreator: testInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pageLabels: basePageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockApplicationCompletedPage.mockClear();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
  });

  it("shows application completed page component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(await screen.findByTestId("application-completed-page")).toBeInTheDocument();
  });

  it("shows header with correct creator network label", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });

  it("passes correct labels to ApplicationCompletedPage component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        labels: basePageLabels.applicationCompletePageLabels
      })
    );
  });

  it("passes correct INTERESTED_CREATOR_REAPPLY_PERIOD flag to ApplicationCompletedPage", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        INTERESTED_CREATOR_REAPPLY_PERIOD: false
      })
    );
  });

  it("passes correct submitted date to ApplicationCompletedPage", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        submittedDate: testInterestedCreator.createdDate
      })
    );
  });

  it("passes correct email to ApplicationCompletedPage", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        emailId: testInterestedCreator.originEmail
      })
    );
  });

  it("passes logout callback to ApplicationCompletedPage", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        onBackButtonClick: expect.any(Function)
      })
    );
  });

  it("passes default thumbnail image to ApplicationCompletedPage", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        applicationCompleteThumbnail: "/img/home-header--980w-x-690h.png"
      })
    );
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...applicationCompleteProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<ApplicationComplete {...propsWithReapplyEnabled} />);

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        INTERESTED_CREATOR_REAPPLY_PERIOD: true
      })
    );
  });

  it("handles missing interested creator data gracefully", async () => {
    const propsWithMinimalData = {
      ...applicationCompleteProps,
      interestedCreator: {
        ...testInterestedCreator,
        createdDate: undefined,
        originEmail: undefined
      }
    };

    expect(() => render(<ApplicationComplete {...propsWithMinimalData} />)).not.toThrow();

    await screen.findByTestId("application-completed-page");

    expect(mockApplicationCompletedPage).toHaveBeenCalledWith(
      expect.objectContaining({
        submittedDate: undefined,
        emailId: undefined
      })
    );
  });
});
