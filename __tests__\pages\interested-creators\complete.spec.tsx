import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";
import ApplicationComplete from "../../../pages/interested-creators/complete";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { ApplicationCompletePageLabels } from "@src/contentManagement/ApplicationCompletePageMapper";

jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsComplete", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const testInterestedCreator = {
    ...anInitialInterestedCreator(),
    contentLanguages: [{ code: "en", name: "English" }],
    createdDate: "2023-12-12T10:00:00Z",
    originEmail: "<EMAIL>"
  };

  const basePageLabels = {
    applicationCompletePageLabels: {
      title: "Application Complete",
      description: "Thank you for your submission",
      buttonText: "Back to Home"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationCompletePageLabels & CommonPageLabels;

  const applicationCompleteProps = {
    locale,
    interestedCreator: testInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pageLabels: basePageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
  });

  it("shows remote complete component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with logout callback configured", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");

    expect(dynamicComponent).toBeInTheDocument();
  });

  it("shows header with correct labels", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });

  it("passes correct props to ApplicationCompletedPage component", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");

    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...applicationCompleteProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<ApplicationComplete {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("handles different locale correctly", async () => {
    const propsWithDifferentLocale = {
      ...applicationCompleteProps,
      locale: "fr-fr"
    };

    render(<ApplicationComplete {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("displays correct submitted date and email", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");

    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toBeInTheDocument();
  });

  it("uses default thumbnail image", async () => {
    render(<ApplicationComplete {...applicationCompleteProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");

    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toBeInTheDocument();
  });

  it("handles missing interested creator data gracefully", () => {
    const propsWithMinimalData = {
      ...applicationCompleteProps,
      interestedCreator: {
        ...testInterestedCreator,
        createdDate: undefined,
        originEmail: undefined
      }
    };

    expect(() => render(<ApplicationComplete {...propsWithMinimalData} />)).not.toThrow();
  });
});
