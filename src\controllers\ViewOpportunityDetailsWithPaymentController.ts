import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import OpportunitiesHttpClient from "../opportunities/OpportunitiesHttpClient";
import CreatorsWithFlaggedStatusHttpClient from "../creators/CreatorsWithFlaggedStatusHttpClient";
import CreatorWithFlaggedStatus from "../creators/CreatorWithFlaggedStatus";
import OpportunityWithPaymentDetails from "@src/opportunities/OpportunityWithPaymentDetails";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "config";
import { CreatorResponse } from "@eait-playerexp-cn/creator-types";

@Service()
class ViewOpportunityDetailsWithPaymentController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly opportunities: OpportunitiesHttpClient,
    private readonly creators: CreatorsWithFlaggedStatusHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const opportunityId = req.query.id;
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);

    const [opportunities, creators]: [
      opportunities: OpportunityWithPaymentDetails,
      creators: CreatorWithFlaggedStatus | CreatorResponse
    ] = await Promise.all([
      this.opportunities.withOpportunityPayment(opportunityId as string, user.id),
      config.FLAG_PER_PROGRAM_PROFILE ? this.creators.withIdForProgramProfile(user.id) : this.creators.withId(user.id)
    ]);

    const resp = {
      opportunity: opportunities,
      creator: creators
    };

    this.json(res, resp);
  }
}

export default ViewOpportunityDetailsWithPaymentController;
