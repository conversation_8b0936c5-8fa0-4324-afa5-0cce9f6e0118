import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import ParticipationsHttpClient from "../opportunities/ParticipationsHttpClient";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ClaimGameCodeController extends Request<PERSON><PERSON>ler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly participations: ParticipationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const participationId = req.body.participationId as string;

    await this.participations.claimGameCode(participationId);

    this.empty(res, HttpStatus.OK);
  }
}

export default ClaimGameCodeController;
