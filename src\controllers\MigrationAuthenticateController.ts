import { NextApiResponse } from "next";
import AuthenticateCreatorAction from "../actions/AuthenticateCreatorAction";
import FindValidRegistrationCodeAction from "../actions/ValidRegistrationCodes/FindValidRegistrationCodeAction";
import CachedTermsAndConditions from "../pactSafe/CachedTermsAndConditions";
import AuthenticateCreatorInput from "../actions/AuthenticateCreatorInput";
import FutureCreator from "../notifications/FutureCreator";
import User from "../authentication/User";
import UnknownCreator from "../creators/UnknownCreator";
import FindValidRegistrationCodeInput from "../actions/ValidRegistrationCodes/FindValidRegistrationCodeInput";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

/** @deprecated Registration is now open and Game Changers have mostly been migrated */
@Service()
class MigrationAuthenticateController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly authenticate: AuthenticateCreatorAction,
    private readonly findCode: FindValidRegistrationCodeAction,
    private readonly termsAndConditions: CachedTermsAndConditions
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const locale = this.locale(req);
    const urlLocale = this.localePathSegment(req);

    try {
      const code = this.query(req, "code");
      const creator = await this.authenticate.execute(new AuthenticateCreatorInput(code as string));
      if (creator?.isDisabled()) {
        const futureCreator = { email: creator.originEmail(), nucleusId: creator.nucleusId() } as FutureCreator;
        await this.addToSession(req, "notify", futureCreator);
        this.redirectTo(res, `${urlLocale}login-error`);
        return;
      }

      await this.addToSession(req, "user", User.fromCreator(creator));

      if (creator?.isUnregistered()) {
        // covers new and exiting registrations (Game changers and creators)
        this.redirectTo(res, `${urlLocale}onboarding/information`);
      } else if (creator?.isInactive()) {
        this.redirectTo(res, `${urlLocale}terms-and-conditions`);
      } else if (
        !(
          await this.termsAndConditions.signedStatus(
            creator.id as string,
            locale as string,
            creator.additionalInformation?.tier as string
          )
        ).upToDate
      ) {
        this.redirectTo(res, `${urlLocale}terms-and-conditions`);
      } else {
        if (this.hasSession(req, "initialPage")) {
          const initialPage = this.session(req, "initialPage") as string;
          await this.removeFromSession(req, "initialPage");
          this.redirectTo(res, initialPage);
        } else {
          this.redirectTo(res, `${urlLocale}dashboard`);
        }
      }
    } catch (exception) {
      if (!(exception instanceof UnknownCreator)) {
        throw exception;
      }

      if (!this.hasSession(req, "registrationCode")) {
        const futureCreator = { email: exception.originEmail, nucleusId: exception.nucleusId };
        await this.addToSession(req, "notify", futureCreator as FutureCreator);
        await this.addToSession(req, "nucleusId", exception.nucleusId);
        this.redirectTo(res, `${urlLocale}login-error`);
        return;
      }

      try {
        const registrationCode = this.session(req, "registrationCode") as string;
        const input = new FindValidRegistrationCodeInput(registrationCode, exception.originEmail);
        await this.findCode.execute(input);

        await this.addToSession(req, "user", User.signUp(exception));
        const futureCreator = {
          email: exception.originEmail,
          nucleusId: exception.nucleusId,
          dateOfBirth: exception.dateOfBirth
        };
        await this.addToSession(req, "futureCreator", futureCreator as FutureCreator);

        this.redirectTo(res, `${urlLocale}onboarding/information`);
      } catch {
        this.redirectTo(res, `${urlLocale}access-error`);
      }
    }
  }
}

export default MigrationAuthenticateController;
