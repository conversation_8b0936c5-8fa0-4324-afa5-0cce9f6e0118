.rewards-page {
  @apply flex flex-col;
}
.reward-hero-web {
  @apply flex flex-col md:flex md:flex-row lg:flex-row;
  background-size: auto;
}

.reward-hero-container {
  @apply flex w-full flex-col items-center justify-center px-meas8;
  background-repeat: no-repeat, no-repeat;
  background-image: var(--rewards-hero-background-circle), var(--rewards-hero-background-square),
    linear-gradient(to right top, #ff4747, #0d1042);
  background-position: left 0 top -46px, right -152px top 185px, left center;
}

@media screen and (min-width: 768px) {
  .reward-hero-container {
    @apply px-meas0;
    background-image: var(--rewards-hero-background-circle-large), var(--rewards-hero-background-square),
      linear-gradient(to right top, #ff4747, #0d1042);
    background-position: left -91px top -486px, right -212px top -22px, left center;
  }
}
@media screen and (min-width: 1024px) {
  .reward-hero-container {
    @apply px-meas0;
    background-image: var(--rewards-hero-background-circle-large), var(--rewards-hero-background-square-large),
      linear-gradient(to right top, #ff4747, #0d1042);
    background-position: left -91px top -333px, right -287px top 20px, left center;
  }
}

@media screen and (min-width: 1280px) {
  .reward-hero-container {
    @apply px-meas0;
    background-image: var(--rewards-hero-background-circle-large), var(--rewards-hero-background-square-large),
      linear-gradient(to right top, #ff4747, #0d1042);
    background-position: left -127px top -327px, right -187px top -22px, left center;
  }
}

.reward-game-access {
  @apply flex w-full flex-col justify-center md:h-auto md:flex-row-reverse md:items-center md:justify-around xl:justify-end;
}

.reward-game-access-container {
  @apply mt-meas26 md:mt-auto;
}

.reward-game-access-left,
.reward-game-access-right {
  @apply relative w-auto md:w-1/2;
}

.reward-game-access-left {
  @apply grid h-auto px-meas0 md:pl-meas8 md:pr-[77px] xl:pl-meas28 xl:pr-[182px];
  grid-gap: 0.5rem;
  grid-template-columns: auto;
  grid-template-rows: 100px auto auto;
}

.reward-competition {
  @apply flex-col md:flex md:flex-row md:items-center md:justify-between;
}

.reward-competition-games > div > section {
  @apply max-w-screen-2xl self-stretch;
}

section.reward-competition > aside {
  @apply flex flex-col items-center justify-center;
}
@media screen and (min-width: 1280px) {
  .reward-competition-games .reward-competition {
    padding-left: 5rem;
  }
}

.reward-hero-container > section {
  @apply max-w-screen-2xl self-stretch md:self-auto;
}

.reward-competition-games > div[class*="-container"] {
  @apply flex w-full flex-col items-center justify-center 3xl:flex-row;
  flex-shrink: 0;
}

.reward-competition-description {
  @apply hidden;
}
.reward-competition-description-mobile {
  @apply py-meas8 font-text-regular text-gray-10  xs:text-mobile-body-large md:pt-meas16 md:text-tablet-body-large lg:text-desktop-body-large;
}
.reward-game-access-right {
  @apply flex flex-col;
}
.reward-game-access-title {
  @apply flex justify-center text-center font-display-bold font-bold text-gray-10 xs:text-mobile-h3 md:w-full md:justify-start md:text-left md:text-tablet-h3 lg:text-desktop-h3;
}
.reward-game-access-description {
  @apply font-text-regular  text-gray-10 xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.reward-games-and-channel-sponsor {
  @apply flex max-w-screen-2xl flex-col;
}
.reward-competition-container {
  @apply w-full pt-[41px] md:pt-meas0 xl:mt-[-256px];
}
.reward-competition-left-or-center {
  @apply flex w-full justify-end md:w-[420px] lg:w-[570px] xl:w-[620px];
}
.reward-competition-left-or-center > .join-an-a-list {
  @apply w-full object-cover;
}
.reward-competition-left-or-center > figure {
  @apply w-[48%];
}
.reward-competition-left-or-center > figure > img {
  min-width: 100%;
}
.reward-competition-left-or-center > figure > figcaption {
  @apply relative flex h-[75px] w-[75px] items-end justify-center overflow-hidden rounded-full bg-custom-2;
  top: -46px;
  left: 30%;
}

.reward-competition-left-or-center > figure > figcaption > div {
  @apply h-[69px] w-[64px] bg-no-repeat;
}
.reward-competition-left-or-center .avatar-left {
  @apply bg-reward-competition-left-avatar;
}
.reward-competition-left-or-center .avatar-right {
  @apply bg-reward-competition-right-avatar;
}

.reward-competition article {
  @apply md:box-border md:w-2/5 md:pl-meas16 md:pt-meas16;
}

.reward-stream-container {
  @apply relative top-[10px] z-[1] flex flex-col items-center justify-center;
}

.reward-live-stream {
  @apply rounded-2xl bg-indigo-50 px-meas2 font-text-regular text-gray-10 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.reward-hero-web-title-small {
  @apply mb-meas7 flex items-center pt-meas12 font-text-regular text-white xs:text-mobile-body-default md:pt-meas24 md:text-tablet-body-default lg:max-w-xl lg:text-desktop-body-default;
}
.reward-hero-web-title {
  @apply mb-meas7 flex w-[85%] items-center font-display-regular font-bold text-white  xs:text-mobile-display md:w-full md:text-tablet-display lg:text-desktop-display xl:w-[74%];
}
.reward-hero-web-sub-title {
  @apply font-text-regular text-white  xs:text-mobile-h5 md:max-w-md md:pb-meas24 md:text-tablet-h5 lg:w-meas44 lg:max-w-2xl lg:pb-meas27 lg:text-desktop-h5;
}
.reward-hero-web-left {
  @apply transform pb-meas16 md:w-[55%] md:pb-meas0  md:pl-meas16 md:pr-meas0 lg:pl-meas33 xl:w-[60%];
}
.reward-hero-web-right-web {
  @apply flex -rotate-7.16 transform items-end justify-start self-center md:w-[45%] md:translate-x-[-10%] md:transform md:self-end xl:w-[40%] xl:translate-x-[-30%] 2xl:translate-x-[2%];
  filter: drop-shadow(-17px 12px 20px rgba(0, 0, 0, 0.2));
}
.reward-hero-web-image {
  @apply ml-auto pt-meas18 md:mr-meas8 lg:mr-auto;
}

.reward-hero-mobile {
  @apply block bg-gradient-to-tr from-indigo-50 to-navy-80 md:hidden;
  background-size: auto;
}
.reward-hero-mobile-title-small {
  @apply mb-meas7 flex items-center pt-meas27 font-bold text-white xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.reward-hero-mobile-title {
  @apply mb-meas12 flex max-w-xs items-center font-bold text-white xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.reward-hero-mobile-sub-title {
  @apply pb-meas24  text-white xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.reward-hero-mobile-left {
  @apply transform bg-no-repeat pl-meas16 pr-meas8;
}
.reward-hero-mobile-right {
  @apply flex -rotate-7.16 transform flex-row-reverse items-end bg-hero-image-reward bg-cover bg-no-repeat;
  filter: drop-shadow(-17px 12px 20px rgba(0, 0, 0, 0.2));
}
.reward-hero-mobile-image {
  @apply ml-auto mr-auto pt-meas18;
}
.reward-hero-mobile-left-bg {
  @apply absolute object-cover;
  z-index: 0;
}

.reward-more {
  @apply flex flex-col pt-meas32;
}

.reward-more-container {
  @apply w-full bg-navy-80 bg-reward-image-carousal bg-no-repeat pb-meas28 2xl:bg-cover;
}

.reward-more > article {
  @apply flex flex-col items-center font-display-regular text-gray-10 xs:text-mobile-h5 md:w-full md:text-tablet-h5 lg:text-desktop-h5;
}

.reward-more-title {
  @apply font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.reward-more-content {
  @apply h-full p-meas12 pb-meas18 font-text-regular  text-gray-10 xs:text-mobile-body-large md:pt-meas20 md:text-tablet-body-large lg:text-desktop-body-large;
}

.reward-image-carousal {
  @apply text-gray-10 xs:pb-meas18 xs:pl-meas18 md:pb-meas32 md:pl-meas32;
}

.reward-competition-games {
  @apply flex flex-col  items-center justify-center bg-navy-80 bg-no-repeat px-meas8 text-center md:bg-reward-competition-games md:px-meas0 md:text-left 3xl:bg-none;
}
.channel-container {
  @apply block justify-evenly py-meas0 md:mt-meas0 md:flex md:px-meas12;
}
.channel-image-container {
  @apply my-auto ml-meas0 w-full md:ml-[6rem] md:w-1/2;
}
.channel-content-container {
  @apply my-auto w-full text-center md:ml-meas24 md:mr-[6rem] md:mr-meas20 md:w-1/2 md:text-left xl:ml-meas0;
}
.sponsor-container {
  @apply block justify-evenly pb-meas32 md:flex md:px-meas16 md:py-meas24 md:pb-meas24 xl:py-meas0;
}
.sponsor-image-container {
  @apply order-2 w-full py-meas0 md:mr-meas8 md:w-1/2;
}
.sponsor-content-container {
  @apply order-1 my-auto mr-meas24 w-full text-center md:ml-meas24 md:mr-meas8 md:w-1/2 md:text-left xl:ml-meas34;
}
.channel-content-title {
  @apply mt-meas4 font-display-regular font-bold text-white xs:text-mobile-h2 md:text-tablet-h2 lg:text-desktop-h2;
}
.channel-content-description {
  @apply mt-meas6  font-text-regular text-white xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.sponsor-content-image,
.channel-partnerships-image {
  @apply object-cover;
}
.channel-sponsor {
  @apply mx-auto max-w-screen-2xl;
}
