import React, { FC, memo, useCallback, useEffect, useState } from "react";
import { Button, close, DateInput, filterOne, Icon, Select } from "@eait-playerexp-cn/core-ui-kit";
import Form from "../../../Form";
import { Controller, useFormContext } from "react-hook-form";
import { useRouter } from "next/router";
import { DefaultPaymentDateRange, PaymentsCriteria } from "@src/api/services/PaymentsService";
import { useAppContext } from "@src/context";
import { PaymentFilterProps } from "./PaymentDetailsTab";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export type SelectOptions = {
  label: string;
  value: string;
}[];

export type FilterLabels = {
  filters: string;
  dateRange: string;
  startDate: string;
  endDate: string;
  paymentStatus: string;
  opportunityType: string;
  applyFilters: string;
  startDateRequired: string;
  endDateRequired: string;
  startDateError: string;
  endDateError: string;
  ok: string;
  cancel: string;
  calendar: string;
};

export type PaymentsFormFiltersProps = {
  filterLabels: FilterLabels;
  dateRangeOptions: SelectOptions;
  paymentStatusOptions: SelectOptions;
  opportunityTypeOptions: SelectOptions;
  startDateErrorMessage: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setStartDateErrorMessage: any;
  endDateErrorMessage: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setEndDateErrorMessage: any;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  CN_LAUNCH_DATE: string;
  selectedFilters: PaymentFilterProps[];
  isCreatorCodeAssigned: boolean;
  selectedCriteria: PaymentsCriteria;
  analytics: BrowserAnalytics;
};

export type FilterButtonProps = {
  filterLabels: FilterLabels;
  isPending: boolean;
  startDateErrorMessage: string;
  endDateErrorMessage: string;
};

export type PaymentsFilterFormProps = {
  filterLabels: FilterLabels;
  dateRangeOptions: SelectOptions;
  paymentStatusOptions: SelectOptions;
  opportunityTypeOptions?: SelectOptions;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatePaymentsFilterDetails: (formData: any) => void;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  CN_LAUNCH_DATE: string;
  isMobile: boolean;
  selectedFilters: PaymentFilterProps[];
  isCreatorCodeAssigned: boolean;
  selectedCriteria: PaymentsCriteria;
  analytics: BrowserAnalytics;
};

export const FilterButton = memo(function FilterButton({
  filterLabels,
  isPending,
  startDateErrorMessage,
  endDateErrorMessage
}: FilterButtonProps) {
  const { formState } = useFormContext();

  return (
    <div className="payment-filter-footer">
      <Button
        type="submit"
        spinner={isPending}
        size="sm"
        disabled={
          Object.keys(formState.errors).length !== 0 ||
          formState.isValid === false ||
          isPending ||
          startDateErrorMessage !== "" ||
          endDateErrorMessage !== ""
        }
      >
        {filterLabels.applyFilters}
      </Button>
    </div>
  );
});

export const PaymentsFormFilters = memo(function PaymentsFormFilters({
  filterLabels,
  dateRangeOptions,
  paymentStatusOptions,
  opportunityTypeOptions,
  startDateErrorMessage,
  setStartDateErrorMessage,
  endDateErrorMessage,
  setEndDateErrorMessage,
  defaultPaymentDateRange,
  CN_LAUNCH_DATE,
  selectedFilters,
  isCreatorCodeAssigned,
  selectedCriteria,
  analytics
}: PaymentsFormFiltersProps) {
  const [range, setRange] = useState("");
  const methods = useFormContext();
  const { control, setValue } = methods;
  const { locale } = useRouter();
  const paymentStartDate = defaultPaymentDateRange.startDate;
  const currentDate = LocalizedDate.now();
  const [startDate, setStartDate] = useState(paymentStartDate);
  const [isUpdated, setIsUpdated] = useState(0);
  const [endDate, setEndDate] = useState(defaultPaymentDateRange.endDate);
  const formSelectedValues = {
    range: selectedFilters.find((selectedItem) => selectedItem.code === "range"),
    status: selectedFilters.find((selectedItem) => selectedItem.code === "status"),
    opportunityType: selectedFilters.find((selectedItem) => selectedItem.code === "opportunityType")
  };
  const router = useRouter();

  useEffect(() => {
    if (formSelectedValues?.range?.value) {
      setStartDate(selectedCriteria.startDate as LocalizedDate);
      setEndDate(selectedCriteria.endDate as LocalizedDate);
      setIsUpdated(isUpdated + 1);
    }
  }, []);

  const onDateRangeChange = useCallback(
    (field) => (item) => {
      if (item.value) field.onChange(item);
      else field.onChange("");

      if (field.name === "range") setRange(item.value);

      analytics.appliedDateRangeFilter({ locale: router.locale, selectedDateRange: item.value });
      switch (item.value) {
        case "allTime":
          setStartDate(paymentStartDate);
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "thisMonth":
          setStartDate(LocalizedDate.startOfMonth());
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "past30Days":
          setStartDate(LocalizedDate.subtractFromNow(1, "months"));
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "past90Days":
          setStartDate(LocalizedDate.subtractFromNow(3, "months"));
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "past6Months":
          setStartDate(LocalizedDate.subtractFromNow(6, "months"));
          setIsUpdated(isUpdated + 1);
          setEndDate(currentDate);
          break;
        case "yearToDate":
          setStartDate(LocalizedDate.now().startOfYear());
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "lastYear":
          // Checking whether lastYear date is greater than launch date or not
          let lastYearStartDate = LocalizedDate.subtractFromNow(1, "years").startOfYear();
          const launchDate = LocalizedDate.fromFormattedDate(CN_LAUNCH_DATE);
          lastYearStartDate = lastYearStartDate.isBefore(launchDate) ? launchDate : lastYearStartDate;
          setStartDate(lastYearStartDate);
          setEndDate(LocalizedDate.subtractFromNow(1, "years").endOfYear());
          setIsUpdated(isUpdated + 1);
          break;
      }
    },
    [startDate, endDate]
  );

  const onSelectChange = useCallback(
    (field) => (item) => {
      if (field.name === "status")
        analytics.appliedPaymentStatusFilter({ locale: router.locale, selectedPaymentStatus: item.value });
      else analytics.appliedPaymentTypeFilter({ locale: router.locale, selectedPaymentType: item.value });
      field.onChange(item.value ? item : "");
    },
    []
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onDateChange = (date: any, type: string) => {
    setRange("custom");
    setValue(
      "range",
      dateRangeOptions.find((option) => option.value === "custom")
    );
    if (type === "startDate") setStartDate(LocalizedDate.fromFormattedDate(date));
    if (type === "endDate") setEndDate(LocalizedDate.fromFormattedDate(date));
  };

  useEffect(() => {
    setValue("startDate", startDate);
    setEndDateErrorMessage("");
    if (isNaN(startDate.millisecondsEpoch)) {
      setStartDateErrorMessage(filterLabels.startDateRequired);
    } else if (startDate.isAfter(endDate)) {
      setStartDateErrorMessage(filterLabels.startDateError);
    } else {
      setStartDateErrorMessage("");
    }
  }, [startDate]);

  useEffect(() => {
    setValue("endDate", endDate);
    setStartDateErrorMessage("");
    if (isNaN(endDate.millisecondsEpoch)) {
      setEndDateErrorMessage(filterLabels.endDateRequired);
    } else if (endDate.isBefore(startDate)) {
      setEndDateErrorMessage(filterLabels.endDateError);
    } else {
      setEndDateErrorMessage("");
    }
  }, [endDate]);

  return (
    <div className="payments-filter-form-elements">
      <Controller
        control={control}
        name="range"
        defaultValue={formSelectedValues.range || dateRangeOptions[0]}
        render={({ field, fieldState: { error } }) => (
          <Select
            id="date-range"
            errorMessage={error?.message}
            options={dateRangeOptions}
            label={filterLabels.dateRange}
            onChange={onDateRangeChange(field)}
            selectedOption={dateRangeOptions.find((option) => option.value === range) || formSelectedValues.range}
            skipOnChangeDuringRender
          />
        )}
      />
      <div className="payment-filter-date-container">
        <Controller
          control={control}
          name="startDate"
          defaultValue={startDate.toDate()}
          render={({ field, fieldState: { error } }) => (
            <DateInput
              key={isUpdated}
              errorMessage={error?.message || startDateErrorMessage}
              {...field}
              label={filterLabels.startDate}
              placeholder={filterLabels.startDate}
              maxDate={new Date()}
              locale={locale}
              onChange={(date: Date) => {
                onDateChange(date, "startDate");
                field.onChange(date);
              }}
              value={startDate.toDate()}
              minDate={paymentStartDate.toDate()}
              id={"startDate"}
              format="MM/dd/yy"
              title={filterLabels.calendar}
              cancelText={filterLabels.cancel}
              okText={filterLabels.ok}
              onCancel={(date) => {
                setValue("startDate", LocalizedDate.fromFormattedDate(date.toDateString()));
              }}
            />
          )}
        />
        <Controller
          control={control}
          name="endDate"
          defaultValue={endDate.toDate()}
          render={({ field, fieldState: { error } }) => (
            <DateInput
              key={isUpdated}
              errorMessage={error?.message || endDateErrorMessage}
              {...field}
              label={filterLabels.endDate}
              placeholder={filterLabels.endDate}
              maxDate={new Date()}
              locale={locale}
              onChange={(date) => {
                onDateChange(date, "endDate");
                field.onChange(date);
              }}
              value={endDate.toDate()}
              minDate={paymentStartDate.toDate()}
              id={"endDate"}
              format="MM/dd/yy"
              title={filterLabels.calendar}
              cancelText={filterLabels.cancel}
              okText={filterLabels.ok}
              onCancel={(date) => {
                setValue("endDate", LocalizedDate.fromFormattedDate(date.toDateString()));
              }}
            />
          )}
        />
      </div>
      <Controller
        control={control}
        name="status"
        defaultValue={formSelectedValues.status || paymentStatusOptions[0]}
        render={({ field, fieldState: { error } }) => (
          <Select
            id="payment-status"
            errorMessage={error?.message}
            options={paymentStatusOptions}
            label={filterLabels.paymentStatus}
            onChange={onSelectChange(field)}
            selectedOption={formSelectedValues.status}
            skipOnChangeDuringRender
          />
        )}
      />

      {isCreatorCodeAssigned && (
        <Controller
          control={control}
          name="opportunityType"
          defaultValue={formSelectedValues.opportunityType || opportunityTypeOptions[0]}
          render={({ field, fieldState: { error } }) => (
            <Select
              id="opportunity-type"
              errorMessage={error?.message}
              options={opportunityTypeOptions}
              label={filterLabels.opportunityType}
              onChange={onSelectChange(field)}
              selectedOption={formSelectedValues.opportunityType}
              skipOnChangeDuringRender
            />
          )}
        />
      )}
    </div>
  );
});

const PaymentsFilterForm: FC<PaymentsFilterFormProps> = ({
  filterLabels,
  dateRangeOptions,
  paymentStatusOptions,
  opportunityTypeOptions,
  updatePaymentsFilterDetails,
  defaultPaymentDateRange,
  CN_LAUNCH_DATE,
  isMobile = false,
  selectedFilters,
  isCreatorCodeAssigned,
  selectedCriteria,
  analytics
}) => {
  const {
    dispatch,
    state: { isLoading }
  } = useAppContext() || {};
  const router = useRouter();

  const [show, setShow] = useState(false);
  const [isPending, setIsPending] = useState(isLoading);

  useEffect(() => {
    setIsPending(isLoading);
  }, [isLoading]);

  const handleOutsideClick = useCallback(
    (e) => {
      if (e.target === e.currentTarget) {
        setShow(false);
      }
    },
    [show]
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const stableDispatch = useCallback<any>(dispatch, []);

  const submitHandler = useCallback(
    async (formData) => {
      setIsPending(true);
      updatePaymentsFilterDetails(formData);
    },
    [stableDispatch]
  );

  const [startDateErrorMessage, setStartDateErrorMessage] = useState("");
  const [endDateErrorMessage, setEndDateErrorMessage] = useState("");

  const toggleFilterForm = () => {
    if (!show) analytics.openedPaymentsFiltersForm({ locale: router.locale });
    setShow(!show);
  };

  return (
    <div className="payment-filter-container" onClick={handleOutsideClick}>
      <div className="payments-filter-button">
        <Button variant="secondary" size="sm" onClick={toggleFilterForm}>
          <Icon icon={filterOne} className="payments-filter-button-icon" />
          <span title={filterLabels.filters}>{isMobile ? "" : filterLabels.filters}</span>
        </Button>
      </div>
      {show && (
        <div className={`payment-filter-section ${show ? "show" : "hide"}`}>
          {isMobile && (
            <Icon
              className="payments-filter-close-button"
              icon={close}
              onClick={() => {
                setShow(false);
              }}
            />
          )}
          <Form onSubmit={submitHandler} defaultValues={{}} mode="onChange">
            <PaymentsFormFilters
              {...{
                filterLabels,
                dateRangeOptions,
                paymentStatusOptions,
                opportunityTypeOptions,
                startDateErrorMessage,
                setStartDateErrorMessage,
                endDateErrorMessage,
                setEndDateErrorMessage,
                defaultPaymentDateRange,
                CN_LAUNCH_DATE,
                selectedFilters,
                isCreatorCodeAssigned,
                selectedCriteria,
                analytics
              }}
            />

            <FilterButton {...{ filterLabels, isPending, startDateErrorMessage, endDateErrorMessage, analytics }} />
          </Form>
        </div>
      )}
    </div>
  );
};

export default PaymentsFilterForm;
