export default function labelsApplicationRejected(t, reSubmitRequestDate) {
  return {
    title: t("application-rejected:title"),
    descriptionPara1: t("application-rejected:descriptionPara1"),
    descriptionPara2: t("application-rejected:descriptionPara2"),
    descriptionPara3: t("application-rejected:descriptionPara3"),
    descriptionPara4: t("application-rejected:descriptionPara4"),
    returnToCreatorNetwork: t("application-rejected:returnToCreatorNetwork"),
    submissionReviewed: t("application-rejected:submissionReviewed"),
    submissionReviewedDescription: t("application-rejected:submissionReviewedDescription", { reSubmitRequestDate }),
    gamerTag: t("application-rejected:gamerTag"),
    email: t("application-rejected:email"),
    status: t("application-rejected:status"),
    submissionDate: t("application-rejected:submissionDate"),
    closed: t("application-rejected:closed"),
    reApplyTitle: t("application-rejected:reApplyTitle"),
    reApplyDescription: t("application-rejected:reApplyDescription"),
    reviewAndResubmit: t("application-rejected:reviewAndResubmit")
  };
}
