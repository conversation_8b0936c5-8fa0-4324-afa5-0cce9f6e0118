import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationStartPagePropsController from "./controllers/ApplicationStartPagePropsController";
import ContentManagementService from "@src/api/services/ContentManagementService";

const applicationStartPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationStartPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale
    )
  );

export default applicationStartPageProps;
