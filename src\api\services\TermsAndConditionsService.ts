import { AxiosResponse } from "axios";
import client from "./Client";
import { SigningUrl } from "@src/pactSafe/TermsAndConditionsHttpClient";

export type SignerInformation = {
  businessName: string;
  creatorId: string;
  country: string;
  email: string;
  firstName: string;
  lastName: string;
  screenName: string;
  locale: string;
  tier: string;
};

const clearSignedStatusForTier = async (locale: string): Promise<AxiosResponse<void>> => {
  return (await client.delete(`/api/v2/terms-and-conditions/${locale}`)) as AxiosResponse<void>;
};

const getSigningUrl = async (signerInformation: SignerInformation): Promise<AxiosResponse<SigningUrl>> => {
  return (await client.post("/api/v2/terms-and-conditions/signing-url", {
    body: signerInformation
  })) as AxiosResponse<SigningUrl>;
};

const TermsAndConditionsService = {
  getSigningUrl,
  clearSignedStatusForTier
};

export default TermsAndConditionsService;
