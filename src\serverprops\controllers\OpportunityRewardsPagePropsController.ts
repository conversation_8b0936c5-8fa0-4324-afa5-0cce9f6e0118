import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type OpportunityRewardsProps = {
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
  interestedCreator: boolean;
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
};

export default class OpportunityRewardsPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<OpportunityRewardsProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<OpportunityRewardsProps>> {
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.defaultAvatar, this.program)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        interestedCreator: featureFlags.isInterestedCreatorFlowEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "opportunities-rewards",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
