@import "common-components/card.css";
@import "common-components/hero.css";
@import "common-components/slider.css";
@import "common-components/breadcrumb.css";
@import "common-components/migration.css";
@import "common-components/carousel.css";
@import "common-components/search.css";
@import "common-components/pagination.css";
@import "common-components/content-card.css";
@import "common-components/tabs.css";
@import "common-components/loading.css";
@import "common-components/toast.css";
@import "common-components/error.css";
@import "common-components/form-title.css";
@import "common-components/tool-tip.css";
@import "../components/CreatorDisplayName.css";
@import "common-components/submitContentLayout.css";
@import "common-components/top-navigation.css";

.unauthorized-page {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
.gcn-badge {
  @apply ml-meas8 h-[30px] w-[30px] md:h-[50px] md:w-[51px];
  flex-shrink: 0;
}
