import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { ApplicationAcceptedPageLabels } from "@src/contentManagement/ApplicationAcceptedPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { ApplicationAcceptedProps } from "pages/interested-creators/application-accepted";
import featureFlags from "utils/feature-flags";

export default class ApplicationAcceptedPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ApplicationAcceptedProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<ApplicationAcceptedProps>> {
    const nucleusId = this.identity(req).nucleusId;
    const application = await this.getApplication(nucleusId);

    if (!featureFlags.isInterestedCreatorFlowEnabled() || !application?.isAccepted()) return { notFound: true };
    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : null;
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "applicationAccepted"
    )) as ApplicationAcceptedPageLabels & CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        locale: this.currentLocale,
        pageLabels,
        showInitialMessage
      }
    };
  }

  private async getApplication(nucleusId: number): Promise<InterestedCreatorApplicationStatus> {
    let existingApplication;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplication = await this.applications.forCreatorWithProgram(nucleusId, config.PROGRAM_CODE);
    } else if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      existingApplication = await this.applications.forCreatorWithApplicationStatus(nucleusId as unknown as number);
    } else {
      existingApplication = await this.applications.forCreatorWith(nucleusId as unknown as number);
    }
    if (existingApplication) {
      if (existingApplication?.createdDate) {
        existingApplication.createdDate = this.currentLocale
          ? existingApplication.createdDateformattedWithoutTime(this.currentLocale)
          : existingApplication.createdDate;
      }

      if (existingApplication.canResubmitRequestDate) {
        existingApplication.canResubmitRequestDate = existingApplication.formatResubmitRequestDateWithoutTime(
          this.currentLocale
        );
      }
    }

    return existingApplication;
  }
}
