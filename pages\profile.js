import "reflect-metadata";
import ProfileLayout from "../components/ProfileLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsLegalDocuments from "../config/translations/legal-documents";
import labelsProfile from "../config/translations/profile";
import labelsFranchisesYouPlay from "../config/translations/franchises-you-play";
import labelsInformation from "../config/translations/information";
import labelsCreatorType from "../config/translations/creator-type";
import labelsCommunicationPreferences from "../config/translations/communication-preferences";
import labelsConnectAccounts from "../config/translations/connect-accounts";
import {
  labelsPaymentBanner,
  labelsPaymentDetails,
  labelsPaymentHistoryGrid,
  labelsPaymentInformation
} from "../config/translations/payment-information";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import CreatorsService from "../src/api/services/CreatorsService";
import Information from "../components/profile/Information";
import GamePreferences from "../components/profile/GamePreferences";
import CreatorType from "../components/profile/CreatorType";
import LegalDocuments from "../components/profile/LegalDocuments";
import PaymentInformationPage from "../components/pages/payment-information/PaymentInformationPage";
import ConnectAccounts from "./connect-accounts";
import CommunicationPreferences from "../components/profile/CommunicationPreferences";
import labelsPointOfContact from "../config/translations/point-of-contact";
import { useAppContext } from "../src/context";
import Error from "./_error";
import { ERROR, SESSION_USER, useIsMounted } from "../utils";
import withRegisteredUser from "../src/utils/WithRegisteredUser";
import withTermsAndConditionsUpToDate from "../src/utils/WithTermsAndConditionsUpToDate";
import Loading from "../components/Loading";
import RedirectException from "../src/utils/RedirectException";
import ConnectedAccountsService from "../src/api/services/ConnectedAccountsService";
import BrowserAnalytics, { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import labelsPaymentsFilter from "../config/translations/payments-filter";
import config from "../config";
import flags from "../utils/feature-flags";
import { useDependency } from "../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { mapNotificationsBellLabels } from "../config/translations/mappers/notifications";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../src/serverprops/middleware/SaveInitialPage";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import verifyAccessToProgram from "../src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import profileProps from "../src/serverprops/ProfileProps";
import { CreatorsService as CreatorService } from "@eait-playerexp-cn/creators-http-client";

const ProfileContent = memo(function ProfileContent({
  user,
  myProfileView,
  error,
  pages,
  query,
  legalDocumentsLabels,
  infoLabels,
  creator,
  hardwarePartners,
  countries,
  labels,
  t,
  creatorTypeLabels,
  creatorTypes,
  franchisesYouPlayLabels,
  buttons,
  updateCreator,
  franchises,
  platforms,
  communicationLabels,
  connectAccountslabels,
  layout,
  analytics,
  locale,
  CN_LAUNCH_DATE,
  allCountries
}) {
  switch (query.section) {
    case "connected-accounts":
      return <ConnectAccounts {...{ user, myProfileView, error, pages }} />;
    case "legal-documents":
      return <LegalDocuments {...{ legalDocumentsLabels, layout }} />;
    case "payment-information":
      return (
        <PaymentInformationPage
          {...{
            labels,
            user,
            unhandledError: layout.main.unhandledError,
            CN_LAUNCH_DATE,
            analytics,
            buttonLabel: buttons.remove
          }}
        />
      );
    case "creator-type":
      return (
        creator && (
          <CreatorType
            {...{
              t,
              creatorTypeLabels,
              buttons,
              user,
              creator,
              updateCreator,
              creatorTypes,
              layout,
              analytics
            }}
          />
        )
      );
    case "communication-settings":
      return (
        creator && (
          <CommunicationPreferences
            {...{
              translations: communicationLabels,
              buttons,
              labels: connectAccountslabels,
              layout,
              analytics,
              locale
            }}
          />
        )
      );
    case "game-preferences":
      return (
        creator && (
          <GamePreferences
            {...{
              franchisesYouPlayLabels,
              buttons,
              infoLabels,
              user,
              creator,
              updateCreator,
              franchises,
              platforms,
              layout,
              analytics
            }}
          />
        )
      );
    default:
      return (
        creator && (
          <Information
            {...{
              infoLabels,
              buttons,
              user,
              creator,
              updateCreator,
              hardwarePartners,
              countries,
              layout,
              analytics,
              allCountries
            }}
          />
        )
      );
  }
});

export default memo(function Profile({
  user,
  error,
  pages,
  analytics = new BrowserAnalytics(user),
  CN_LAUNCH_DATE,
  invalidTikTokScope,
  FLAG_COUNTRIES_BY_TYPE,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_NEW_FOOTER_ENABLED
}) {
  const {
    metadataClient,
    creatorsClient,
    errorHandler,
    configuration: { FLAG_PER_PROGRAM_PROFILE, PROGRAM_CODE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const creatorService = useMemo(() => new CreatorService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const { dispatch, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const router = useRouter();
  const isMounted = useIsMounted();
  const { query } = router;
  const [creator, setCreator] = useState(null);
  const [hardwarePartners, setHardwarePartners] = useState(null);
  const [franchises, setFranchises] = useState(null);
  const [platforms, setPlatforms] = useState(null);
  const [creatorTypes, setCreatorTypes] = useState(null);
  const [countries, setCountries] = useState(null);
  const [allCountries, setAllCountries] = useState(null);
  const locale = router.locale;

  const { t } = useTranslation([
    "common",
    "profile",
    "franchises-you-play",
    "creator-type",
    "information",
    "connect-accounts",
    "notifications",
    "opportunities"
  ]);
  const stableT = useCallback(t, []);
  const {
    layout,
    profileLabels,
    franchisesYouPlayLabels,
    infoLabels,
    communicationLabels,
    legalDocumentsLabels,
    creatorTypeLabels,
    connectAccountslabels,
    labels,
    pocLabels,
    notificationsLabels
  } = useMemo(() => {
    const commonLabels = labelsCommon(t);
    const labels = {
      layout: commonLabels,
      profileLabels: labelsProfile(t),
      franchisesYouPlayLabels: labelsFranchisesYouPlay(t),
      infoLabels: {
        ...labelsInformation(t),
        header: { calendar: commonLabels.header.calendar },
        profileLabels: { updateAvatar: labelsProfile(t).updateAvatar }
      },
      communicationLabels: labelsCommunicationPreferences(t),
      legalDocumentsLabels: labelsLegalDocuments(t),
      creatorTypeLabels: labelsCreatorType(t),
      connectAccountslabels: labelsConnectAccounts(t),
      labels: {
        paymentInfoLabels: {
          ...labelsPaymentInformation(t),
          buttons: {
            next: commonLabels.buttons.next,
            prev: commonLabels.buttons.prev,
            close: commonLabels.buttons.close
          }
        },
        paymentBannerLabels: labelsPaymentBanner(t),
        paymentDetailsLabels: labelsPaymentDetails(t),
        paymentHistoryGridLabels: labelsPaymentHistoryGrid(t),
        paymentFilterLabels: {
          ...labelsPaymentsFilter(t),
          buttons: { ok: commonLabels.buttons.ok, cancel: commonLabels.buttons.cancel },
          header: { calendar: commonLabels.header.calendar }
        }
      },
      pocLabels: labelsPointOfContact(t),
      notificationsLabels: mapNotificationsBellLabels(t)
    };
    labels.layout.footer = { locale: locale, labels: labels.layout.footer };
    return labels;
  }, [t]);

  useEffect(() => {
    async function fetchData() {
      if (isMounted()) {
        try {
          // Creators BFF GET
          const creator = FLAG_PER_PROGRAM_PROFILE
            ? await creatorService.getCreator(PROGRAM_CODE)
            : (await CreatorsService.getCreatorWithFlaggedStatus()).data;
          if (FLAG_PER_PROGRAM_PROFILE) {
            creator.additionalInformation.hardwarePartners = creator.additionalInformation.hardwarePartners.map(
              (partner) => ({
                ...partner,
                label: partner.name,
                value: partner.id
              })
            );
            creator.preferredPrimaryPlatform = {
              ...creator.preferredPrimaryPlatform,
              value: creator.preferredPrimaryPlatform.id,
              label: creator.preferredPrimaryPlatform.name
            };
            creator.preferredSecondaryPlatforms = creator.preferredSecondaryPlatforms.map((platform) => {
              return {
                ...platform,
                value: platform.id,
                label: platform.name
              };
            });
            creator.preferredPrimaryFranchise = {
              ...creator.preferredPrimaryFranchise,
              value: creator.preferredPrimaryFranchise.id,
              label: creator.preferredPrimaryFranchise.name
            };
            creator.preferredSecondaryFranchises = creator.preferredSecondaryFranchises.map((platform) => {
              return {
                ...platform,
                value: platform.id,
                label: platform.name
              };
            });
            creator.communicationPreferences.contentLanguages = creator.communicationPreferences.contentLanguages.map(
              (language) => ({
                ...language,
                value: language.code,
                label: language.name
              })
            );
            creator.communicationPreferences.preferredLanguage = creator.program.preferredLanguage;
          }
          setCreator(creator);
          const hardwarePartners = await metadataService.getHardwarePartners();
          hardwarePartners && setHardwarePartners(hardwarePartners);
          const franchises = await metadataService.getFranchises();
          franchises && setFranchises(franchises);
          const platforms = await metadataService.getPlatformsMatching({ type: "SITE" });
          platforms && setPlatforms(platforms);
          const creatorTypes = await metadataService.getCreatorTypes();
          creatorTypes && setCreatorTypes(creatorTypes);
          const countries = FLAG_COUNTRIES_BY_TYPE
            ? await metadataService.getCountriesMatching({ type: "PAYMENTS" })
            : await metadataService.getCountries();
          countries && setCountries(countries);
          const allCountries = FLAG_COUNTRIES_BY_TYPE
            ? await metadataService.getCountriesMatching()
            : await metadataService.getCountries();
          allCountries && setAllCountries(allCountries);
        } catch (e) {
          errorHandler(stableDispatch, e);
        }
      }
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    async function removeFbPages() {
      try {
        // Unset FBPages from session.
        await ConnectedAccountsService.clearFbPages();
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    return () => {
      // Cleared the FB pages session on unmount, as the modal comes up even after refreshing the browser
      removeFbPages();
    };
  }, [pages]);

  useEffect(() => {
    if (isMounted()) {
      user && stableDispatch({ type: SESSION_USER, data: user });
    }
    if (invalidTikTokScope) {
      stableDispatch({ type: ERROR, data: layout.main.unhandledError });
    }
  }, [user, stableDispatch]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    (!creator && (
      <div className="profile-loader">
        <Loading />
      </div>
    )) || (
      <ProfileLayout
        {...layout}
        {...{
          profileLabels,
          pocLabels,
          user,
          creator,
          notificationsLabels,
          analytics,
          FLAG_NEW_NAVIGATION_ENABLED,
          FLAG_NEW_FOOTER_ENABLED
        }}
      >
        <ProfileContent
          {...{
            user,
            myProfileView: true,
            error,
            pages,
            query,
            legalDocumentsLabels,
            infoLabels,
            buttons: layout.buttons,
            creator,
            updateCreator: setCreator,
            hardwarePartners,
            countries,
            labels,
            t: stableT,
            creatorTypeLabels,
            creatorTypes,
            franchisesYouPlayLabels,
            franchises,
            platforms,
            communicationLabels,
            connectAccountslabels,
            layout,
            analytics,
            locale,
            CN_LAUNCH_DATE,
            updateAvatar: profileLabels.updateAvatar,
            allCountries
          }}
        />
        <div />
      </ProfileLayout>
    )
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(profileProps(locale));

    return await router.run(req, res);
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;
  const error = req.session.error || null;
  const { pages = [] } = req.session.fbPages || {};
  const invalidTikTokScope = req.session.INVALID_TIKTOK_SCOPE || null;
  delete req.session.error;
  delete req.session.INVALID_TIKTOK_SCOPE;
  req.session.save();

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      pages,
      error,
      CN_LAUNCH_DATE: config.CN_LAUNCH_DATE,
      ...(await serverSideTranslations(locale, [
        "common",
        "profile",
        "franchises-you-play",
        "information",
        "connect-accounts",
        "communication-preferences",
        "legal-documents",
        "creator-type",
        "payment-information",
        "point-of-contact",
        "notifications",
        "opportunities",
        "payments-filter"
      ])),
      invalidTikTokScope,
      FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled(),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled()
    }
  };
};
