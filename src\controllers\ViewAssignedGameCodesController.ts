import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import ParticipationsHttpClient from "../opportunities/ParticipationsHttpClient";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewAssignedGameCodesController extends Request<PERSON><PERSON>ler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly participations: ParticipationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const participationIds = req.body;

    const assignedGameCodes = await this.participations.viewAssignedGameCodes(participationIds);

    this.json(res, assignedGameCodes);
  }
}

export default ViewAssignedGameCodesController;
