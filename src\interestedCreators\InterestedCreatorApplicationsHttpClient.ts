import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import InterestedCreatorApplication from "./InterestedCreatorApplication";
import InterestedCreatorApplicationStatus from "./InterestedCreatorApplicationStatus";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { HttpStatus } from "@eait-playerexp-cn/http";
import InterestedCreatorApplicationProgram from "./InterestedCreatorApplicationProgram";

@Service()
class InterestedCreatorApplicationsHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#operation/viewCreatorApplicationStatus|View Creator application status}
   */
  async forCreatorWith(nucleusId: number): Promise<InterestedCreatorApplication> {
    try {
      const response = (await this.client.get(`/v1/creator-applications/${nucleusId}`)) as AxiosResponse;
      return new InterestedCreatorApplication(response.data);
    } catch (e) {
      const status = e?.response?.status;
      // There's no application for given nucleusId or nucleusId can't be found
      if (status === HttpStatus.CONFLICT_CODE || status === HttpStatus.NOT_FOUND_CODE) return null;
      throw e;
    }
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Interested-Creators/operation/viewCreatorApplicationStatusWithApplicantInformation|View Creator application status}
   */
  async forCreatorWithApplicationStatus(nucleusId: number): Promise<InterestedCreatorApplicationStatus> {
    try {
      const response = (await this.client.get(`/v2/creator-applications/${nucleusId}`)) as AxiosResponse;
      // This will be called from Approved, Pending and Rejected flow.
      // For Approved status we'll get `applicantInformation = null`
      if (response.data.applicantInformation) {
        response.data.applicantInformation.nucleusId = Number(response.data.applicantInformation.nucleusId);
        response.data.applicantInformation.contentLanguages = response.data.applicantInformation.contentLanguages.map(
          (contentLanguage) => ({ value: contentLanguage.code, label: contentLanguage.name })
        );
        response.data.applicantInformation.contentAccounts = response.data.applicantInformation.contentAccounts.map(
          (contentAccount) => ({ url: contentAccount.uri, followers: contentAccount.followersCount })
        );
      }
      response.data.status = response.data.requestStatus?.status;
      return new InterestedCreatorApplicationStatus(response.data);
    } catch (e) {
      const status = e?.response?.status;
      // There's no application for given nucleusId or nucleusId can't be found
      if (status === HttpStatus.CONFLICT_CODE || status === HttpStatus.NOT_FOUND_CODE) return null;
      throw e;
    }
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Interested-Creators/operation/viewCreatorApplicationStatusWithApplicantCreatorProgramInformation}
   */

  async forCreatorWithProgram(nucleusId: number, programCode: string): Promise<InterestedCreatorApplicationProgram> {
    try {
      const response = (await this.client.get(`/v3/creator-applications/${nucleusId}`, {
        query: { programCode }
      })) as AxiosResponse;
      // This will be called from Approved, Pending and Rejected flow.
      // For Approved status we'll get `applicantInformation = null`
      if (response.data.applicantInformation) {
        response.data.applicantInformation.nucleusId = Number(response.data.applicantInformation.nucleusId);
        response.data.applicantInformation.contentLanguages = response.data.applicantInformation.contentLanguages.map(
          (contentLanguage) => ({ value: contentLanguage.code, label: contentLanguage.name })
        );
        response.data.applicantInformation.contentAccounts = response.data.applicantInformation.contentAccounts.map(
          (contentAccount) => ({ url: contentAccount.uri, followers: contentAccount.followersCount })
        );
      }
      response.data.status = response.data.requestStatus?.status;
      return new InterestedCreatorApplicationProgram(response.data);
    } catch (e) {
      const status = e?.response?.status;
      // There's no application for given nucleusId or nucleusId can't be found
      if (status === HttpStatus.CONFLICT_CODE || status === HttpStatus.NOT_FOUND_CODE) return null;
      throw e;
    }
  }
}

export default InterestedCreatorApplicationsHttpClient;
