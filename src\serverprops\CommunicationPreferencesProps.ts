import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import CommunicationPreferencesPagePropsController from "./controllers/CommunicationPreferencesPagePropsController";
import config from "config";

const communicationPreferencesProps = (locale: string) =>
  serverPropsControllerFactory(
    new CommunicationPreferencesPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default communicationPreferencesProps;
