import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import ParticipationsHttpClient from "../opportunities/ParticipationsHttpClient";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
export default class ParticipationDetailsController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly participations: ParticipationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    const { opportunityId }: { opportunityId: string } = req.body;

    const participationDetails = await this.participations.withCreatorCode(user.id, opportunityId);

    this.json(res, participationDetails);
  }
}
