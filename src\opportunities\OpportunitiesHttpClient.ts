import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import OpportunityCriteria from "../actions/Dashboard/OpportunityCriteria";
import SearchOpportunityWithPerksCriteria from "./SearchOpportunityWithPerksCriteria";
import OpportunitiesWithActivationWindowPage from "./OpportunitiesWithActivationWindowPage";
import OpportunityWithPaymentDetails from "./OpportunityWithPaymentDetails";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import config from "config";

export const opportunitiesApiClient = () => {
  return config.FLAG_OPPORTUNITIES_API_CLIENT === "true" ? "opportunityClient" : "operationsClient";
};

export type SearchOpportunityWithProgram = {
  program: string;
  title?: string;
  perks?: string[];
  page?: number;
  size?: number;
};

export type OpportunityCriteriaWithProgram = {
  page: number;
  size: number;
  status: string;
  programCode: string;
};

@Service()
class OpportunitiesHttpClient {
  constructor(@Inject(opportunitiesApiClient()) private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Opportunities/operation/viewOpportunityWithPayment}
   */
  async withOpportunityPayment(id: string, creatorId: string): Promise<OpportunityWithPaymentDetails> {
    const response = (await this.client.get(`/v9/opportunities/${id}/creator/${creatorId}`)) as AxiosResponse;
    return OpportunityWithPaymentDetails.fromApi(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Participations/operation/viewMyOpportunitiesWithMeetingDetails}
   */
  async matchingWithEventDetails(
    creatorId: string,
    criteria: OpportunityCriteria
  ): Promise<OpportunitiesWithActivationWindowPage> {
    const response = (await this.client.get(`/v4/creators/${creatorId}/participations`, {
      query: criteria
    })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://dev-services.cn.ea.com/cn-opportunities-api/swagger-ui/index.html?configUrl=/cn-opportunities-api/v3/api-docs/swagger-config#/view-participations-controller/viewParticipationsWithProgramCode}
   */
  async matchingWithEventDetailsWithProgram(
    creatorId: string,
    criteria: OpportunityCriteriaWithProgram
  ): Promise<OpportunitiesWithActivationWindowPage> {
    const response = (await this.client.get(`/v5/creators/${creatorId}/participations`, {
      query: criteria
    })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Creators/operation/searchOpportunitiesWithMeetingDetails}
   */
  async searchOpportunitiesWithEventDetails(
    id: string,
    criteria: SearchOpportunityWithPerksCriteria
  ): Promise<OpportunitiesWithActivationWindowPage> {
    const response = (await this.client.post(`/v4/creators/${id}/opportunities`, { body: criteria })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://opportunities-api-eait-playerexp-cn-cn-services--f205b0587239a9.gitlab.ea.com/docs/api.html#tag/Opportunities/operation/searchOpportunitiesWithProgram}
   */
  async searchOpportunitiesWithProgram(
    id: string,
    criteria: SearchOpportunityWithProgram
  ): Promise<OpportunitiesWithActivationWindowPage> {
    const response = await this.client.post(`/v5/creators/${id}/opportunities`, { body: criteria });
    return Promise.resolve(response.data);
  }
}

export default OpportunitiesHttpClient;
