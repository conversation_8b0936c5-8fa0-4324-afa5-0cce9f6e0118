import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";
import { NotificationCardLabels, NotificationCardMapper } from "./NotificationCardMapper";

export type NotificationPageLabels = {
  notificationsPageLabels: {
    title: string;
    noNotification: {
      title: string;
      description: string;
    };
    viewAllNotification: string;
    dismissAllNotification: string;
    viewAll: string;
    dismissAll: string;
    dismiss: string;
    cardLabels: NotificationCardLabels;
  };
};

export class NotificationsPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): NotificationPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      notificationsPageLabels: {
        title: microCopy.get("notificationsPage.title"),
        noNotification: {
          title: microCopy.get("notificationsPage.noNotification.title"),
          description: microCopy.get("notificationsPage.noNotification.description")
        },
        viewAllNotification: microCopy.get("notificationsPage.viewAllNotifications"),
        dismissAllNotification: microCopy.get("notificationsPage.dismissAllNotifications"),
        dismissAll: microCopy.get("notificationsPage.dismissAll"),
        viewAll: microCopy.get("notificationsPage.viewAll"),
        dismiss: microCopy.get("notificationsPage.dismiss"),
        cardLabels: NotificationCardMapper.map(microCopy)
      }
    };
  }
}
