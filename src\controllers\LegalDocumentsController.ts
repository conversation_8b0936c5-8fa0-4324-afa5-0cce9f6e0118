import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import LegalDocumentsHttpClient from "../legalDocuments/LegalDocumentsHttpClient";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "../../config";

@Service()
export default class LegalDocumentsController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly legalDocuments: LegalDocumentsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const creator = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    const creatorId = creator.id;

    const legalDocuments = await this.legalDocuments.allWithSignature(creatorId);

    this.json(res, legalDocuments);
  }
}
