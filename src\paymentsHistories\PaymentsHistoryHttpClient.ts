import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import PaymentsHistory from "./PaymentsHistory";
import { PaymentsCriteria } from "../api/services/PaymentsService";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class PaymentsHistoryHttpClient {
  constructor(@Inject("paymentClient") private client: TraceableHttpClient) {}

  async matching(id: string, criteria: PaymentsCriteria): Promise<PaymentsHistory> {
    const response = (await this.client.get(`/v1/creators/${id}/payment-history`, {
      query: criteria
    })) as AxiosResponse;
    return Promise.resolve(response.data as PaymentsHistory);
  }
}

export default PaymentsHistoryHttpClient;
