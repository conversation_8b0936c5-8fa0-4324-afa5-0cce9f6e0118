import React, { FC, memo, useCallback } from "react";
import Loading from "../../../Loading";
import PaymentBanner, { PaymentBannersLabelsProps } from "./PaymentBanner";
import PaymentStatusAmounts from "./PaymentStatusAmounts";
import {
  DefaultPaymentDateRange,
  DollarAmount,
  FormattedAmount,
  PaymentsCriteria,
  PaymentsHistory
} from "@src/api/services/PaymentsService";
import TransactionGrid from "./TransactionHistory/TransactionGrid";
import MobileTransactionGrid from "./TransactionHistory/MobileTransactionGrid";
import PaymentsFilterForm from "./PaymentsFilterForm";
import { PaymentFilterLabelsProps } from "../PaymentInformationPage";
import { Icon, outlineClose } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "@src/context";
import { DEFAULT_PAGE } from "../../../dashboard/Pagination";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

const FILTER_TYPE = {
  RANGE: "range",
  STATUS: "status",
  TYPE: "opportunityType"
};

export type TransactionHistoryLabelProps = {
  paymentGridDescription: string;
  paymentGridType: string;
  paymentGridStatus: string;
  paymentGridAmountDue: string;
  paymentGridDate: string;
  paymentGridDateHelp: string;
  paymentGridContract: string;
  statusPending: string;
  statusProcessed: string;
  downloadContract: string;
  typeCreatorCode: string;
  typeOpportunity: string;
  noPayments: string;
  noPaymentsDescription: string;
  noPaymentsLink: string;
  downloadContractLabel?: string;
  opportunityImageLabel?: string;
  noProcessedPayments: string;
  noProcessedPaymentsDescription: string;
  noPendingPayments: string;
  noPendingPaymentsDescription: string;
  opportunityTitle: string;
  simsMakerProgram: string;
};

export type PaymentDetailsLabelsProps = {
  paymentOverview: string;
  paymentTotalPaid: string;
  paymentPendingPayments: string;
  transactionHistory: string;
  transactionHistoryDetails: string;
  paymentInformationClick: string;
  filteredBy: string;
};

export type PaginationProps = {
  next: string;
  prev: string;
  pages: number[];
  currentPage: number;
  onPageChange: (number) => void;
};

export type PaymentDetailsProps = {
  isMobile?: boolean;
  paymentsHistory: PaymentsHistory;
  setPaymentsHistory?: React.Dispatch<React.SetStateAction<PaymentsHistory>>;
  isPayable: boolean;
  labels: {
    paymentBannerLabels: PaymentBannersLabelsProps;
    paymentDetailsLabels: PaymentDetailsLabelsProps;
    paymentHistoryGridLabels: TransactionHistoryLabelProps;
    paymentFilterLabels: PaymentFilterLabelsProps;
    buttonLabel: string;
  };
  locale: string;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  onPaymentBannerClick?: () => void;
  paginationProps: PaginationProps | null;
  isShowingPagination: boolean;
  selectedCriteria: PaymentsCriteria;
  setSelectedCriteria?: React.Dispatch<React.SetStateAction<PaymentsCriteria>>;
  selectedFilters: PaymentFilterProps[];
  setSelectedFilters?: React.Dispatch<React.SetStateAction<PaymentFilterProps[]>>;
  resetDateRangeFields?: () => void;
  initializePagination?: (totalTransactions: number) => void;
  CN_LAUNCH_DATE: string;
  isCreatorCodeAssigned: boolean;
  analytics: BrowserAnalytics;
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
};

export type PaymentFilterProps = {
  label: string;
  value: string;
  code: string;
};

const PaymentDetailsTab: FC<PaymentDetailsProps> = ({
  paymentsHistory,
  isPayable,
  labels,
  isMobile = false,
  locale,
  defaultPaymentDateRange,
  onPaymentBannerClick,
  isShowingPagination,
  paginationProps,
  selectedCriteria,
  setSelectedCriteria,
  resetDateRangeFields,
  selectedFilters,
  setSelectedFilters,
  CN_LAUNCH_DATE,
  isCreatorCodeAssigned,
  FLAG_NEW_NAVIGATION_ENABLED,
  analytics
}) => {
  const { paymentBannerLabels, paymentDetailsLabels, paymentFilterLabels } = labels;
  let { paymentHistoryGridLabels } = labels;

  if (selectedCriteria.status === "PAID") {
    paymentHistoryGridLabels = {
      ...paymentHistoryGridLabels,
      noPayments: paymentHistoryGridLabels.noProcessedPayments,
      noPaymentsDescription: paymentHistoryGridLabels.noProcessedPaymentsDescription,
      noPaymentsLink: null
    };
  }

  const {
    state: { isLoading }
  } = useAppContext() || {};
  const router = useRouter();

  if (selectedCriteria.status === "PENDING") {
    paymentHistoryGridLabels = {
      ...paymentHistoryGridLabels,
      noPayments: paymentHistoryGridLabels.noPendingPayments,
      noPaymentsDescription: paymentHistoryGridLabels.noPendingPaymentsDescription,
      noPaymentsLink: null
    };
  }

  const updatePaymentsFilterDetails = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (formData: any) => {
      const newSelectedFilters = [] as PaymentFilterProps[];
      const criteria = {
        page: DEFAULT_PAGE
      } as PaymentsCriteria;

      if (formData?.range.value !== "allTime") {
        newSelectedFilters.push({
          label: formData?.range.label,
          value: formData?.range.value,
          code: FILTER_TYPE.RANGE
        });
        criteria.startDate = formData?.startDate;
        criteria.endDate = formData?.endDate;
      } else {
        criteria.startDate = defaultPaymentDateRange?.startDate;
        criteria.endDate = defaultPaymentDateRange?.endDate;
      }

      if (formData?.status.value !== "ALL") {
        newSelectedFilters.push({
          label: formData?.status.value === "PAID" ? paymentFilterLabels.status.processed : formData?.status.label,
          value: formData?.status.value,
          code: FILTER_TYPE.STATUS
        });
        criteria.status = formData?.status.value;
      }

      if (formData?.opportunityType && formData?.opportunityType?.value !== "ALL") {
        newSelectedFilters.push({
          label:
            formData?.opportunityType.value === "marketing_opportunity"
              ? paymentFilterLabels.type.opportunity
              : formData?.opportunityType.value === "sims_ugx_opportunity"
              ? paymentFilterLabels.type.simsMakerProgram
              : paymentFilterLabels.type.creatorCode,
          value: formData?.opportunityType.value,
          code: FILTER_TYPE.TYPE
        });
        criteria.opportunityType = formData?.opportunityType.value;
      }

      analytics.appliedAllPaymentFilters({
        locale: router.locale,
        selectedPaymentStatus: formData?.status?.value || "",
        selectedPaymentType: formData?.opportunityType?.value || "",
        selectedDateRange: formData?.range?.value || ""
      });

      setSelectedCriteria(criteria);
      setSelectedFilters(newSelectedFilters);
    },
    [paymentsHistory]
  );

  const removeFilter = (removedFilter: PaymentFilterProps) => {
    analytics.removedPaymentFilter({
      locale: router.locale,
      removedFilteredValue: removedFilter.value,
      removedFilterType: removedFilter.code
    });

    setSelectedFilters((selectedFilters) => {
      return selectedFilters.filter((selectedItem) => selectedItem.label !== removedFilter.label);
    });

    if (removedFilter.code === "status" || removedFilter.code === "opportunityType") {
      setSelectedCriteria((criteria) => {
        return {
          ...criteria,
          [removedFilter.code]: "ALL",
          page: DEFAULT_PAGE
        };
      });
    }
    if (removedFilter.code === "range") {
      resetDateRangeFields();
    }
  };

  const paymentsFilterOptions = {
    filterLabels: {
      filters: paymentFilterLabels.filters,
      dateRange: paymentFilterLabels.dateRange,
      startDate: paymentFilterLabels.startDate,
      endDate: paymentFilterLabels.endDate,
      paymentStatus: paymentFilterLabels.paymentStatus,
      opportunityType: paymentFilterLabels.opportunityType,
      applyFilters: paymentFilterLabels.applyFilters,
      startDateRequired: paymentFilterLabels.startDateRequired,
      endDateRequired: paymentFilterLabels.endDateRequired,
      startDateError: paymentFilterLabels.startDateError,
      endDateError: paymentFilterLabels.endDateError,
      ok: paymentFilterLabels.buttons.ok,
      cancel: paymentFilterLabels.buttons.cancel,
      calendar: paymentFilterLabels.header.calendar
    },
    dateRangeOptions: [
      { label: paymentFilterLabels.range.allTime, value: "allTime" },
      { label: paymentFilterLabels.range.thisMonth, value: "thisMonth" },
      { label: paymentFilterLabels.range.past30Days, value: "past30Days" },
      { label: paymentFilterLabels.range.past90Days, value: "past90Days" },
      { label: paymentFilterLabels.range.past6Months, value: "past6Months" },
      { label: paymentFilterLabels.range.yearToDate, value: "yearToDate" },
      { label: paymentFilterLabels.range.lastYear, value: "lastYear" },
      { label: paymentFilterLabels.range.custom, value: "custom" }
    ],
    paymentStatusOptions: [
      { label: paymentFilterLabels.status.all, value: "ALL" },
      { label: paymentFilterLabels.status.processed, value: "PAID" },
      { label: paymentFilterLabels.status.pending, value: "PENDING" }
    ],
    opportunityTypeOptions: [
      { label: paymentFilterLabels.type.all, value: "ALL" },
      { label: paymentFilterLabels.type.opportunity, value: "marketing_opportunity" },
      { label: paymentFilterLabels.type.creatorCode, value: "support_a_creator" },
      { label: paymentFilterLabels.type.simsMakerProgram, value: "sims_ugx_opportunity" }
    ]
  };

  return isLoading ? (
    <div className="loader">
      <Loading />
    </div>
  ) : (
    <div className="payment-settings payments-details">
      {!isPayable && (
        <PaymentBanner {...{ labels: paymentBannerLabels, onPaymentBannerClick, FLAG_NEW_NAVIGATION_ENABLED }} />
      )}

      <section className="payment-details-overview">
        <header className="payment-details-overview-header">
          <h4 className="payment-details-heading payment-details-overview-heading">
            {paymentDetailsLabels.paymentOverview}
          </h4>
          <PaymentsFilterForm
            {...{
              ...paymentsFilterOptions,
              updatePaymentsFilterDetails,
              defaultPaymentDateRange,
              CN_LAUNCH_DATE,
              isMobile,
              selectedFilters,
              isCreatorCodeAssigned,
              selectedCriteria,
              analytics
            }}
          />
        </header>

        {selectedFilters.length > 0 && (
          <div className="payment-details-filters">
            <p className="payments-filter-title">{paymentDetailsLabels.filteredBy} </p>
            {selectedFilters.map((selectedItem) => (
              <div className="payments-filter-selected-item" key={selectedItem.value}>
                <span
                  data-testid={`opportunities-selected-perks-item-label-${selectedItem.value}`}
                  className="opportunities-selected-perks-item-label"
                >
                  {selectedItem.label}
                </span>
                <button
                  aria-label={labels.buttonLabel}
                  className="payments-filter-selected-item-remove"
                  onClick={() => {
                    removeFilter(selectedItem);
                  }}
                >
                  <Icon icon={outlineClose} className="payments-filter-selected-item-remove-icon" />
                </button>
              </div>
            ))}
          </div>
        )}

        <p className="payment-details-date-range">
          {(selectedCriteria.startDate as LocalizedDate).formatWithEpoch("MMM DD, YYYY", locale)} -{" "}
          {(selectedCriteria.endDate as LocalizedDate).formatWithEpoch("MMM DD, YYYY", locale)}
        </p>

        {
          <PaymentStatusAmounts
            paidAmountProps={{
              isDisabled: !isPayable || selectedCriteria?.status === "PENDING",
              payment:
                (paymentsHistory?.totalPaidAmount?.abbreviateOn("M") as FormattedAmount) ||
                (new DollarAmount("0").abbreviateOn("M") as FormattedAmount),
              title: paymentDetailsLabels.paymentTotalPaid,
              isAmountZero: paymentsHistory?.totalPaidAmount?.isZero()
            }}
            pendingAmountProps={{
              variant: "pending-payment",
              isDisabled: !isPayable || selectedCriteria?.status === "PAID",
              payment:
                (paymentsHistory?.totalPendingAmount?.abbreviateOn("M") as FormattedAmount) ||
                (new DollarAmount("0").abbreviateOn("M") as FormattedAmount),
              title: paymentDetailsLabels.paymentPendingPayments,
              isAmountZero: paymentsHistory?.totalPendingAmount?.isZero()
            }}
          />
        }
      </section>

      <section className="payment-details-transaction-history">
        <header>
          <h4 className="payment-details-heading payment-details-transaction-heading">
            {paymentDetailsLabels.transactionHistory}
          </h4>
          <p className="payment-details-transaction-history-text">{paymentDetailsLabels.transactionHistoryDetails}</p>
        </header>

        {(!isMobile || paymentsHistory?.details?.length === 0) && (
          <TransactionGrid
            {...{ paymentsHistory, isShowingPagination, labels: paymentHistoryGridLabels, paginationProps, analytics }}
          />
        )}
        {isMobile && paymentsHistory?.details?.length > 0 && (
          <MobileTransactionGrid
            {...{ paymentsHistory, isShowingPagination, labels: paymentHistoryGridLabels, paginationProps, analytics }}
          />
        )}
      </section>
    </div>
  );
};

export default memo(PaymentDetailsTab);
