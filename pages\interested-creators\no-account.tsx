import "reflect-metadata";
import React, { ComponentType, useCallback, useMemo } from "react";
import flags from "../../utils/feature-flags";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import withNoAccountCreator from "../../src/utils/WithNoAccountCreator";
import BrowserAnalytics, {
  AuthenticatedUser,
  AuthenticatedUserFactory,
  InitialInterestedCreator
} from "../../src/analytics/BrowserAnalytics";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import Layout, { LayoutBody } from "../../components/Layout";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import Loading from "../../components/Loading";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import noAccountPageProps from "@src/serverprops/NoAccountPageProps";
import { addTelemetryInformation } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { NoAccountLabels } from "@src/contentManagement/NoAccountPageMapper";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";
import { Identity } from "@eait-playerexp-cn/identity-types";
import ExplorePages, { PageProps } from "@components/pages/interested-creators/ExplorePages";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

const NoAccountPage: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/NoAccountPage"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type NoAccountType = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InitialInterestedCreator | Identity;
  analytics?: BrowserAnalytics;
  showInitialMessage?: boolean;
  user: AuthenticatedUser;
  pageLabels: NoAccountLabels & ApplicationStartPageLabels & CommonPageLabels;
  locale: string;
};

export default function NoAccount({
  interestedCreator,
  user,
  pageLabels,
  analytics = new BrowserAnalytics(user)
}: NoAccountType): JSX.Element {
  const { noAccountLabels, commonPageLabels, applicationStartPageLabels } = pageLabels;
  const { explore, exploreLeftTitle, exploreRightTitle, perks } = applicationStartPageLabels;
  const { creatorNetwork, close, how } = commonPageLabels;
  const { configuration: config } = useDependency();
  const { state: { isLoading } = {} } = useAppContext();
  const router = useRouter();
  const explorePages: PageProps[] = useMemo(
    () => [
      {
        title: exploreLeftTitle,
        image: "../img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: how,
        href: "/how-it-works"
      },
      {
        title: exploreRightTitle,
        image: "../img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: perks,
        href: "/opportunities-rewards"
      }
    ],
    [exploreLeftTitle, exploreRightTitle, how, perks]
  );
  const startApplication = useCallback(() => {
    analytics.startedCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push(
      config.FLAG_PER_PROGRAM_PROFILE ? `/api/applications` : `${config.APPLICATIONS_MFE_BASE_URL}/api/applications`
    );
  }, [router, config, analytics]);
  const logout = useCallback(() => {
    router.push("/api/logout");
  }, [router]);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        {isLoading && <Loading />}
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} onClose={logout} />
          <div className="mg-bg"> </div>
          <NoAccountPage
            email={
              (interestedCreator as Identity).email || (interestedCreator as InitialInterestedCreator).originEmail || ""
            }
            labels={noAccountLabels}
            startApplication={startApplication}
            noAccountThumbnail={"/img/home-header--980w-x-690h.png"}
          />
          <ExplorePages title={explore} explorePages={explorePages} />;
        </div>
      </LayoutBody>
    </Layout>
  );
}
export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(noAccountPageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<NoAccountType>;
  }

  const interestedCreator = await withNoAccountCreator(req, res);
  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  await addTelemetryInformation(req, res, () => {});
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "noAccount");
  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator),
      locale,
      pageLabels,
      showInitialMessage: req.session.showInitialMessage || false
    }
  };
};
