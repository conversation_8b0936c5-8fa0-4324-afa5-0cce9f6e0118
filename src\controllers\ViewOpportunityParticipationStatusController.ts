import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import ParticipationsHttpClient from "../opportunities/ParticipationsHttpClient";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
export default class ViewOpportunityParticipationStatusController
  extends AuthenticatedRequestHandler
  implements Controller
{
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly participations: ParticipationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    const { opportunityIds }: { opportunityIds: Array<string> } = req.body;

    const participationStatuses = await this.participations.withSubmissionStatus(user.id, opportunityIds);

    this.json(res, participationStatuses);
  }
}
