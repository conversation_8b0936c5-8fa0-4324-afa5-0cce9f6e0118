import {
  NextApiRequestWithSession,
  Request<PERSON><PERSON><PERSON>,
  <PERSON>quest<PERSON><PERSON>lerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { AccessErrorProps } from "pages/access-error";

export default class AccessErrorPagePropsController
  extends Request<PERSON><PERSON><PERSON>
  implements ServerPropsController<AccessErrorProps>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<AccessErrorProps>> {
    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : false;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        ...(await serverSideTranslations(this.currentLocale, ["common", "access-error"])),
        showInitialMessage
      }
    };
  }
}
