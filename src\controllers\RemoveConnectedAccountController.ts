import { Inject, Service } from "typedi";
import ConnectedAccountsHttpClient from "../accounts/ConnectedAccountsHttpClient";
import { NextApiResponse } from "next";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";
import config from "../../config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class RemoveConnectedAccountController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly connectedAccounts: ConnectedAccountsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const type: string = config.FLAG_PER_PROGRAM_PROFILE
      ? this.identity(req).type
      : this.hasSession(req, "nucleusId")
      ? "INTERESTED_CREATOR"
      : "CREATOR";
    const accountId = req.query.id as string;
    await this.connectedAccounts.removeConnectedAccount(accountId, type);
    this.empty(res, HttpStatus.OK);
  }
}

export default RemoveConnectedAccountController;
