import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import ConnectedAccountsHttpClient from "../accounts/ConnectedAccountsHttpClient";
import config from "../../config";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewConnectedAccountsController extends Request<PERSON><PERSON>ler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly connectedAccounts: ConnectedAccountsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const nucleusId = +(req.query.nucleusId as string);
    const accounts = config.INTERESTED_CREATOR_REAPPLY_PERIOD
      ? await this.connectedAccounts.getAllConnectedAccountsWithExpirationStatus(nucleusId)
      : await this.connectedAccounts.getAllConnectedAccounts(nucleusId);

    await this.addToSession(req, "nucleusId", nucleusId);

    this.json(res, accounts);
  }
}

export default ViewConnectedAccountsController;
