import ValidRegistrationsCodes from "./ValidRegistrationsCodes";
import { Inject, Service } from "typedi";
import ValidRegistrationCodeCriteria from "./ValidRegistrationCodeCriteria";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

/**
 * @deprecated
 */
@Service()
class ValidRegistrationCodesHttpClient implements ValidRegistrationsCodes {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async matching(criteria: ValidRegistrationCodeCriteria): Promise<void> {
    await this.client.post(`/v1/verify-invite-code`, { body: criteria });
  }
}

export default ValidRegistrationCodesHttpClient;
