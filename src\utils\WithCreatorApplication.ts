import "reflect-metadata";
import { NextApiResponse } from "next";
import InterestedCreatorApplicationsHttpClient from "../interestedCreators/InterestedCreatorApplicationsHttpClient";
import ApiContainer from "../ApiContainer";
import config from "../../config";
import InterestedCreatorApplicationStatus from "../interestedCreators/InterestedCreatorApplicationStatus";
import { addTelemetryInformation, NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";

/**
 * @deprecated
 */
export default async function withCreatorApplication(
  req: NextApiRequestWithSession,
  res: NextApiResponse,
  locale?: string
): Promise<InterestedCreatorApplicationStatus> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());
  await addTelemetryInformation(req, res, () => Promise.resolve());
  const nucleusId = req.session.creatorNucleusId;
  const applications = ApiContainer.get(InterestedCreatorApplicationsHttpClient);
  let existingApplication;
  if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
    existingApplication = await applications.forCreatorWithProgram(nucleusId as unknown as number, config.PROGRAM_CODE);
  } else if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
    existingApplication = await applications.forCreatorWithApplicationStatus(nucleusId as unknown as number);
  } else {
    existingApplication = await applications.forCreatorWith(nucleusId as unknown as number);
  }
  if (existingApplication) {
    existingApplication.createdDate = locale
      ? existingApplication.createdDateformattedWithoutTime(locale)
      : existingApplication.createdDate;

    if (existingApplication.canResubmitRequestDate) {
      existingApplication.canResubmitRequestDate = existingApplication.formatResubmitRequestDateWithoutTime(locale);
    }
  }

  return Promise.resolve(existingApplication);
}
