import { Controller, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import type { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { Inject, Service } from "typedi";
import User from "../../src/authentication/User";
import { DEFAULT_LOCALE, getLocale } from "../../utils";
import NormalizedLocale from "../../src/locales/NormalizedLocale";
import config from "config";
import { NextApiResponse } from "next";

/**
 * @deprecated Please use the controller from `@eait-playerexp-cn/authentication` package
 */
@Service()
class LegacyRedirectToLoginController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    let user: User = req.session.user as User;
    const isExistingCreator = req.session.isExistingCreator;
    const locale = req.cookies.NEXT_LOCALE || getLocale(req.headers, config.SUPPORTED_LOCALES);
    const urlLocale = `${(locale !== DEFAULT_LOCALE && "/" + locale) || ""}`;

    const accessError = (req.session?.accessError as string) ?? "";
    if (!user || req.session.interestedCreator || accessError === "no_access") {
      await this.removeFromSession(req, accessError);
      const uri = `${config.LOGIN_URL}&locale=${NormalizedLocale.fromSlug(locale || DEFAULT_LOCALE)}`;
      res.setHeader("Location", uri);
      res.status(302);
      res.end();
    } else {
      user = User.from(user);
      const uri: string =
        user.isUnregistered() || isExistingCreator ? `${urlLocale}/onboarding/information` : `${urlLocale}/dashboard`;
      this.redirectTo(res, uri);
    }
  }
}

export default LegacyRedirectToLoginController;
