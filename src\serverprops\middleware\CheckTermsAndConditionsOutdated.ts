import { CheckTermsAndConditionsOutdatedServerPropsMiddleware } from "@eait-playerexp-cn/identity";
import ApiContainer from "@src/ApiContainer";
import CachedTermsAndConditions from "@src/pactSafe/CachedTermsAndConditions";
import { serverPropsMiddlewareFactory } from "@eait-playerexp-cn/server-kernel";

const checkTermsAndConditionsOutdated = (locale: string) => {
  return serverPropsMiddlewareFactory(
    new CheckTermsAndConditionsOutdatedServerPropsMiddleware(
      ApiContainer.get("options"),
      ApiContainer.get(CachedTermsAndConditions),
      locale
    )
  );
};

export default checkTermsAndConditionsOutdated;
