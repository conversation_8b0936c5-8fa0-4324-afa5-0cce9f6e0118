import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import DisclosurePagePropsController from "./controllers/DisclosurePagePropsController";
import config from "config";

const disclosureProps = (locale: string) =>
  serverPropsControllerFactory(
    new DisclosurePagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default disclosureProps;
