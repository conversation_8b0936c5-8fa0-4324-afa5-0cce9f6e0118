import client from "./Client";

export type ErrorType = {
  code: string;
  message: string;
};

type SelectedPage = {
  pageId: string;
  pageAccessToken: string;
};

const clearAccountType = async (): Promise<void> => {
  await client.delete("/api/account-types");
};

const clearFbPages = async (): Promise<void> => {
  await client.delete("/api/facebook-connect");
};

const removeDiscordAccount = async (id: string): Promise<void> => {
  await client.delete(`/api/discord-accounts/${id}`);
};

const connectFbPages = async (selectedPage: SelectedPage): Promise<void> => {
  await client.post("/api/facebook-channels", { body: selectedPage });
};

const removeConnectedAccount = async (accountId: string): Promise<void> => {
  await client.delete(`/api/accounts/${accountId}`);
};
const deleteConnectedAccountErrors = async (): Promise<ErrorType> => {
  const response = await client.delete("/api/sessions/error");
  return response.data;
};

const AccountsService = {
  clearFbPages,
  connectFbPages,
  clearAccountType,
  removeConnectedAccount,
  deleteConnectedAccountErrors,
  removeDiscordAccount
};

export default AccountsService;
