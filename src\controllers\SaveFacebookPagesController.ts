import ConnectedAccountsHttpClient from "../accounts/ConnectedAccountsHttpClient";
import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import config from "../../config";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class SaveFacebookPagesController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly connectedAccounts: ConnectedAccountsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    if (this.query(req, "error") === "access_denied") {
      this.html(res, "<script>window.close();</script>");
      return;
    } else if (!config.hasAllFacebookScopes(this.query(req, "granted_scopes") as string)) {
      await this.addToSession(req, "error", {
        code: "invalid-facebook-scope",
        message: "Cannot connect a Facebook Page, not enough authorization scopes were selected"
      });
      this.html(res, "<script>window.close();</script>");
      return;
    }
    const pages = await this.connectedAccounts.facebookPages(this.query(req, "code") as string);
    await this.addToSession(req, "fbPages", pages);
    this.html(res, "<script>window.close();</script>");
  }
}
export default SaveFacebookPagesController;
