import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import CreatorWithCreatorPrograms from "./CreatorWithCreatorPrograms";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithProgramsHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Creators/operation/viewCreatorWithProgramAndPreferredValuesAndLinks}
   */
  async withId(id: string): Promise<CreatorWithCreatorPrograms> {
    const response = (await this.client.get(`/v7/creators/${id}`)) as AxiosResponse;
    return Promise.resolve(CreatorWithCreatorPrograms.fromApi(response.data));
  }
}

export default CreatorsWithProgramsHttpClient;
