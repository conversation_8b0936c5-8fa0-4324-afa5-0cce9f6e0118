import AccountInformation, { AccountInformationResponse } from "./AccountInformation";
import PreferredFranchise, { PreferredFranchiseResponse } from "../franchises/PreferredFranchise";
import MailingAddress, { MailingAddressResponse } from "./MailingAddress";
import PreferredPlatform, { PreferredPlatformResponse } from "../platforms/PreferredPlatform";
import CommunicationPreferences, { CommunicationPreferencesResponse } from "./CommunicationPreferences";
import ConnectedChannel from "../channels/ConnectedChannel";
import AdditionalInformation, { AdditionalInformationResponse } from "./AdditionalInformation";
import LegalEntityInformation, { LegalEntityInformationResponse } from "./LegalEntityInformation";
import config from "../../config";
import crypto from "crypto";
import Identity from "../authentication/Identity";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export type CreatorResponse = {
  id: string;
  avatar: string;
  creatorTypes: string[];
  accountInformation: AccountInformationResponse;
  preferredPlatforms: PreferredPlatformResponse[];
  preferredFranchises: PreferredFranchiseResponse[];
  mailingAddress: MailingAddressResponse;
  connectedChannels: ConnectedChannel[];
  additionalInformation: AdditionalInformationResponse;
  legalInformation: LegalEntityInformationResponse;
  communicationPreferences: CommunicationPreferencesResponse;
};

/** @deprecated This class will be removed as soon we migrate to v2 */
export default class Creator {
  private preferredPrimaryPlatforms: PreferredPlatform | null;
  private preferredSecondaryPlatforms: Array<PreferredPlatform>;
  private preferredPrimaryFranchises: PreferredFranchise | null;
  private preferredSecondaryFranchises: Array<PreferredFranchise>;
  readonly hasPointOfContact: boolean;
  readonly pointOfContactName?: string;
  readonly hasPOCDiscord: boolean;
  readonly pocDiscordTag?: string;
  protected readonly hashedId?: string;

  static fromApi(data: CreatorResponse): Creator {
    return new Creator(
      data.id,
      data.avatar,
      data.creatorTypes || [],
      AccountInformation.fromApi(data.accountInformation),
      data.preferredPlatforms?.map((item) => PreferredPlatform.fromApi(item)),
      data.preferredFranchises?.map((item) => PreferredFranchise.fromApi(item)),
      MailingAddress.fromApi(data.mailingAddress),
      CommunicationPreferences.fromApi(data.communicationPreferences),
      data.connectedChannels || [],
      AdditionalInformation.fromApi(data.additionalInformation),
      data.legalInformation ? LegalEntityInformation.fromApi(data?.legalInformation) : null
    );
  }

  constructor(
    readonly id: string,
    readonly avatar: string | null,
    readonly creatorTypes: Array<string>,
    public accountInformation: AccountInformation,
    preferredPlatforms: Array<PreferredPlatform>,
    preferredFranchises: Array<PreferredFranchise>,
    readonly mailingAddress: MailingAddress,
    readonly communicationPreferences: CommunicationPreferences,
    readonly connectedChannels: Array<ConnectedChannel>,
    readonly additionalInformation: AdditionalInformation,
    readonly legalEntity?: LegalEntityInformation
  ) {
    this.avatar = avatar || `${config.DEFAULT_AVATAR_IMAGE}`;
    this.preferredPrimaryPlatforms = preferredPlatforms?.filter((item) => item.isPrimary() === true)[0] || null;
    this.preferredSecondaryPlatforms = preferredPlatforms?.filter((item) => item.isPrimary() === false);
    this.preferredPrimaryFranchises = preferredFranchises?.filter((item) => item.isPrimary() === true)[0] || null;
    this.preferredSecondaryFranchises = preferredFranchises?.filter((item) => item.isPrimary() === false);
    this.hasPointOfContact = this.additionalInformation.pointOfContact !== null;
    this.pointOfContactName = this.hasPointOfContact ? this.additionalInformation.pointOfContact.name : undefined;
    this.pocDiscordTag = this.hasPointOfContact ? this.additionalInformation.pointOfContact.discordTag : null;
    this.hasPOCDiscord = this.hasPointOfContact && this.pocDiscordTag !== null;
  }

  isDisabled(): boolean {
    return this.accountInformation.status === "DISABLED";
  }

  isUnregistered(): boolean {
    return this.accountInformation.status === "UNREGISTERED";
  }

  isInactive(): boolean {
    return this.accountInformation.status === "INACTIVE";
  }

  originEmail(): string {
    return this.accountInformation?.originEmail;
  }

  nucleusId(): number {
    return this.accountInformation?.nucleusId;
  }

  username(): string {
    return this.accountInformation?.defaultGamerTag;
  }

  dateOfBirth(): number {
    return this.accountInformation?.dateOfBirth;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  syncIdentity(identity: Identity): any {
    const updatedIdentity = {
      id: this.id,
      accountInformation: {
        ...this.accountInformation,
        defaultGamerTag: identity.defaultGamerTag,
        originEmail: identity.originEmail,
        lastLoginDate: LocalizedDate.formattedNow()
      }
    };
    this.accountInformation = updatedIdentity.accountInformation;
    return updatedIdentity;
  }

  connectedAccountsTypes(): string[] {
    return this?.connectedChannels?.map((account) => account.type) || [];
  }

  contentLanguages(): string {
    return this?.communicationPreferences?.contentLanguages?.map((language) => language.label).join(",") || "";
  }

  creatorTypesLabels(): string[] {
    return this?.creatorTypes || [];
  }

  primaryFranchise(): string {
    return this?.preferredPrimaryFranchises?.label || "";
  }

  primaryPlatform(): string {
    return this?.preferredPrimaryPlatforms?.label || "";
  }

  secondaryFranchises(): string[] {
    return this?.preferredSecondaryFranchises?.map((franchise) => franchise.label) || [];
  }

  secondaryPlatforms(): string[] {
    return this?.preferredSecondaryPlatforms?.map((platform) => platform.label) || [];
  }

  analyticsId(): string {
    return this.hashedId
      ? this.hashedId
      : crypto.createHash("sha256").update(this.accountInformation.nucleusId.toString()).digest("base64");
  }

  pointOfContact(): string | undefined {
    return this?.pointOfContactName;
  }
}
