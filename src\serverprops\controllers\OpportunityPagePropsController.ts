import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type OpportunityProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
  locale: string;
};

export default class OpportunityPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<OpportunityProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<OpportunityProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        locale: this.currentLocale,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "notifications",
          "connect-accounts",
          "opportunities"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled()
      }
    };
  }
}
