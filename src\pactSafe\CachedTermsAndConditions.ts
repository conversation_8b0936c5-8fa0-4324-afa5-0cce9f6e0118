import {
  SignedStatus,
  TermsAndConditionsStatusCriteria,
  TermsAndConditionsStatuses,
  TermsAndConditionsStatusesHttpClient
} from "@eait-playerexp-cn/identity";
import { Inject, Service } from "typedi";
import TermsAndConditionsHttpClient from "./TermsAndConditionsHttpClient";
import config from "../../config";
import { RedisCache } from "@eait-playerexp-cn/server-kernel";

@Service()
export default class CachedTermsAndConditions implements TermsAndConditionsStatuses {
  private static readonly CACHE_KEY = "signedStatus";

  constructor(
    @Inject("termsAndConditions")
    private readonly termsAndConditions: TermsAndConditionsHttpClient,
    @Inject("statuses")
    private readonly statuses: TermsAndConditionsStatusesHttpClient,
    private readonly cache: RedisCache
  ) {}

  async matching(criteria: TermsAndConditionsStatusCriteria): Promise<SignedStatus> {
    const { creatorId, locale, program } = criteria;
    const cacheKey: string = this.getCacheKey(creatorId, locale, program);

    if (await this.cache.has(cacheKey)) return (await this.cache.get(cacheKey)) as SignedStatus;

    const status = await this.statuses.matching(criteria);
    await this.cache.set(cacheKey, status, config.TERMS_STATUS_CACHE_TTL);

    return status;
  }

  async signedStatus(creatorId: string, locale: string, program?: string): Promise<SignedStatus> {
    const cacheKey: string = this.getCacheKey(creatorId, locale, program);
    if (await this.cache.has(cacheKey)) {
      return (await this.cache.get(cacheKey)) as SignedStatus;
    }
    const status = await this.termsAndConditions.signedStatus(creatorId, locale);
    await this.cache.set(cacheKey, status, config.TERMS_STATUS_CACHE_TTL);

    return Promise.resolve(status);
  }

  async signedStatusWithProgram(creatorId: string, locale: string, program: string): Promise<SignedStatus> {
    const cacheKey: string = this.getCacheKey(creatorId, locale, program);
    if (await this.cache.has(cacheKey)) {
      return (await this.cache.get(cacheKey)) as SignedStatus;
    }
    const status = await this.termsAndConditions.signedStatusWithProgram(creatorId, locale, program);
    await this.cache.set(cacheKey, status, config.TERMS_STATUS_CACHE_TTL);

    return Promise.resolve(status);
  }

  async clearSignedStatusForProgram(creatorId: string, locale: string, program: string): Promise<void> {
    const cacheKey: string = this.getCacheKey(creatorId, locale, program);

    return this.cache.delete(cacheKey);
  }

  private getCacheKey(creatorId: string, locale: string, program?: string): string {
    return `${CachedTermsAndConditions.CACHE_KEY}_${creatorId}_${locale}_${program}`;
  }
}
