import "reflect-metadata";
import React from "react";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { render, screen } from "@testing-library/react";
import { mockMatchMedia } from "../../helpers/window";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { CreatorTypePageLabels } from "@src/contentManagement/CreatorTypePageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import CreatorTypes from "pages/interested-creators/creator-types";
import { useDependency } from "@src/context/DependencyContext";
import { anInitialInterestedCreator } from "__tests__/factories/initialInterestedCreators/InitialInterestedCreator";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsCreatorTypes", () => {
  const mockDispatch = jest.fn();

  const initialInterestedCreator = anInitialInterestedCreator({
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email()
  });
  const pageLabels = {
    informationLabels: {},
    breadcrumbPageLabels: {},
    creatorTypePageLabels: {
      labels: {
        youtuber: "Youtuber",
        vlogger: "Vlogger",
        photographer: "Photographer",
        designer_artist: "Designer/Artist",
        blogger: "Blogger",
        live_streamer: "Live Streamer",
        podcaster: "Podcaster",
        cosplayer: "Cosplayer",
        animator: "Animator",
        screenshoter: "Screenshoter",
        lifestyle: "Lifestyle",
        other: "Other"
      },
      messages: {
        creatorTypes: ""
      }
    },
    commonPageLabels: {}
  } as InformationPageLabels & BreadcrumbPageLabels & CreatorTypePageLabels & CommonPageLabels;
  const interestedCreatorsCreatorTypesProps = {
    interestedCreator: initialInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    analytics: {} as unknown as BrowserAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: pageLabels
  };
  const router = { locale: "en-us", push: jest.fn() };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: false
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      metadataClient: {},
      configuration: { BASE_PATH: "" }
    });
  });

  it("shows remote creator type component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with exception code and shows error page", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: 500,
        sessionUser: { id: "test-user" },
        isLoading: false
      }
    });

    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    expect(screen.queryByTestId("dynamic")).not.toBeInTheDocument();
  });

  it("displays loading state when isLoading is true", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: true
      }
    });

    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with different page labels", async () => {
    const propsWithDifferentLabels = {
      ...interestedCreatorsCreatorTypesProps,
      pageLabels: {
        ...interestedCreatorsCreatorTypesProps.pageLabels,
        creatorTypePageLabels: {
          ...interestedCreatorsCreatorTypesProps.pageLabels.creatorTypePageLabels,
          labels: {
            ...interestedCreatorsCreatorTypesProps.pageLabels.creatorTypePageLabels.labels,
            youtuber: "Custom YouTuber Label"
          }
        }
      }
    };

    render(<CreatorTypes {...propsWithDifferentLabels} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("passes correct props to CreatorType component", async () => {
    render(<CreatorTypes {...interestedCreatorsCreatorTypesProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    
    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...interestedCreatorsCreatorTypesProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<CreatorTypes {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
