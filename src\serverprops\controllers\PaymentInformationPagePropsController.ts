import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { PaymentInformationProps } from "pages/payment-information";
import featureFlags from "utils/feature-flags";

export default class PaymentInformationPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<PaymentInformationProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<PaymentInformationProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        locale: this.currentLocale,
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "payment-information",
          "payments-filter",
          "notifications"
        ])),
        CN_LAUNCH_DATE: config.CN_LAUNCH_DATE,
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled()
      }
    };
  }
}
