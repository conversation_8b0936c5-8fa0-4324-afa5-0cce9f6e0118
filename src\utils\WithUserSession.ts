import { NextApiResponse } from "next";
import User from "../authentication/User";
import { NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";

/**
 * @deprecated
 */
export default async function withUserSession(
  req: NextApiRequestWithSession,
  res: NextApiResponse
): Promise<User | null> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());

  const user = (req.session.user as User) || null;

  return Promise.resolve(user);
}
