import { HttpStatus } from "@eait-playerexp-cn/http";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

type CreatorTypeProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
};
export default class CreatorTypePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<CreatorTypeProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<CreatorTypeProps>> {
    if (this.hasIdentity(req) && this.identity(req).hasAccessTo(this.program)) {
      return {
        redirect: {
          destination: `${this.localePathSegment(req, this.currentLocale)}dashboard`,
          statusCode: HttpStatus.FOUND_CODE
        }
      };
    }
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, ["common", "breadcrumb", "creator-type", "opportunities"]))
      }
    };
  }
}
