import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type MyContentPageProps = {
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
  FLAG_CONTENT_WITH_FINAL_REMARK: boolean;
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  locale: string;
};

export default class MyContentPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<MyContentPageProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<MyContentPageProps>> {
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.defaultAvatar, this.program)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        locale: this.currentLocale,
        FLAG_CONTENT_WITH_FINAL_REMARK: featureFlags.isContentWithFinalRemarksEnabled(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "my-content",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
