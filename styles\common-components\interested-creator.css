@import "../Interested-creators/interested-creators-layout.css";

.interested-creator.mg-container {
  @apply pb-meas0;
}
.interested-creator section {
  @apply w-full;
}
.interested-creator section > form {
  @apply w-full pt-meas32;
}
.interested-creators-creator-type-container .card-container {
  @apply border-t border-white border-opacity-[0.33] pt-meas32;
}
@media screen and (min-width: 768px) {
  .interested-creator .slider-content {
    transform: none !important; /*Unfortunately had to do this to override inline style*/
  }
}
.interested-creator .mg-page {
  min-height: -webkit-fill-available;
  min-height: -moz-available;
  min-height: stretch;
}
.interested-creator .mg-header-container {
  @apply flex-1;
}
.interested-creator .mg-header-logo a {
  @apply flex items-center;
}
.interested-creator .mg-header-close {
  @apply mr-meas7 mt-meas0;
}
.interested-creator .mg-header {
  @apply items-center;
}
.interested-creator {
  @apply px-meas0 pb-meas0;
}
.interested-creator .mg-header-container {
  @apply px-meas8;
}
.interested-creator .mg-page form {
  @apply w-[100%];
}
.ic-footer-container {
  @apply sticky bottom-meas0 w-full border-t-0 border-white border-opacity-[0.33] bg-white py-meas8 pl-meas8 pr-[1rem] text-right md:pr-[3rem] lg:pr-[10.5rem];
}
.ic-footer-container svg.icon.icon-hide {
  @apply hidden;
}
.ic-footer-container .btn:first-child {
  @apply mr-meas8;
}
