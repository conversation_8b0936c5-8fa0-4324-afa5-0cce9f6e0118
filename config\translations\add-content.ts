export default function labelsAddContent(t, accountConnected) {
  return {
    addContent: t("add-content:addContent"),
    addNewContent: t("add-content:addNewContent"),
    addContentInstruction: t("add-content:addContentInstruction"),
    reviewContent: t("add-content:reviewContent"),
    title: t("add-content:title"),
    description: t("add-content:description"),
    opportunityHeading: t("add-content:opportunityHeading"),
    urlTitle: t("add-content:urlTitle"),
    urlPlaceholder: t("add-content:urlPlaceholder"),
    addMoreUrlLabel: t("add-content:addMoreUrlLabel"),
    accountInformation1: t("add-content:accountInformation1"),
    accountInformation2: t("add-content:accountInformation2"),
    clickTheIcon: t("add-content:clickTheIcon"),
    contentInformation1: t("add-content:contentInformation1"),
    contentInformation2: t("add-content:contentInformation2"),
    contentInformation3: t("add-content:contentInformation3"),
    modalTitle: t("add-content:modalTitle"),
    modalDescription: t("add-content:modalDescription"),
    no: t("add-content:no"),
    yes: t("add-content:yes"),
    contentSubmissionSucessTitle: t("add-content:contentSubmissionSucessTitle", { accountConnected }),
    contentSubmissionSucessDescription: t("add-content:contentSubmissionSucessDescription", { accountConnected }),
    connectAnAccount: t("add-content:connectAnAccount")
  };
}
