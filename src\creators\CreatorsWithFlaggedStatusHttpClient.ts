import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import CreatorWithFlaggedStatus from "./CreatorWithFlaggedStatus";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { CreatorResponse } from "@eait-playerexp-cn/creator-types";

@Service()
class CreatorsWithFlaggedStatusHttpClient {
  constructor(@Inject("operationsClient") private readonly client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithFlaggedStatus> {
    const response = (await this.client.get(`/v6/creators/${id}`)) as AxiosResponse;
    return Promise.resolve(CreatorWithFlaggedStatus.fromApi(response.data));
  }

  async withIdForProgramProfile(nucleusIdOrCreatorId: string | number): Promise<CreatorResponse> {
    const response = await this.client.get(`/v9/creators/${nucleusIdOrCreatorId}`);
    return response.data;
  }
}

export default CreatorsWithFlaggedStatusHttpClient;
