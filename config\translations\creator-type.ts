export default function labelsCreatorType(t) {
  return {
    title: t("creator-type:title"),
    interestedCreatorTitle: t("creator-type:interestedCreatorTitle"),
    interestedCreatorDescription: t("creator-type:interestedCreatorDescription"),
    infoTitle: t("creator-type:infoTitle"),
    modalConfirmationTitle: t("common:modalConfirmationTitle"),
    confirmationDesc1: t("common:confirmationDesc1"),
    confirmationDesc2: t("common:confirmationDesc2"),
    creatorType: t("common:creatorType"),
    successMsgHeader: t("creator-type:success:updatedInformationHeader"),
    successMsgContent: t("creator-type:success:creatorType")
  };
}
