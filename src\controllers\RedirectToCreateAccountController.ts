import { NextApiResponse } from "next";
import config from "../../config";
import NormalizedLocale from "../locales/NormalizedLocale";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class RedirectToCreateAccountController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const locale = this.locale(req);

    this.redirectTo(res, `${config.CREATE_ACCOUNT_URL}&locale=${NormalizedLocale.fromSlug(locale)}`);
  }
}

export default RedirectToCreateAccountController;
