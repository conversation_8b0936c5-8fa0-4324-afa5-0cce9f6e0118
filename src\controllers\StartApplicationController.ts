import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
export default class StartApplicationController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.addToSession(req, "interestedCreator", true);
    this.redirectTo(res, "/api/login");
  }
}
