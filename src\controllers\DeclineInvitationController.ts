import { Inject, Service } from "typedi";
import { NextApiRequest, NextApiResponse } from "next";
import DeclinedInvitationsHttpClient from "../../src/opportunities/DeclinedInvitationsHttpClient";
import { Controller, RequestHandler, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
export default class DeclineInvitationController extends Request<PERSON><PERSON>ler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly invitation: DeclinedInvitationsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    const invitationId = this.query(req, "id") as string;
    await this.invitation.declineInvitation(invitationId);
    this.empty(res, HttpStatus.OK);
  }
}
