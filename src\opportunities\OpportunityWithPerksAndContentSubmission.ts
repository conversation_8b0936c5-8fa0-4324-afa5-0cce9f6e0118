import OpportunityGameCode from "./OpportunityGameCode";
import Perk from "../perks/Perk";
import OpportunityContentSubmission from "./OpportunityContentSubmission";
import { Platform } from "@eait-playerexp-cn/metadata-types";

export default class OpportunityWithPerksAndContentSubmission {
  readonly platformOptions: Array<Platform>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly platformRegionOptions: Array<any>;

  constructor(
    readonly id: string,
    readonly title?: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    readonly registrationPeriod?: any,
    readonly heroImage?: string,
    readonly backgroundImage?: string,
    readonly gameTitle?: string,
    readonly hasEvent?: boolean,
    readonly hasPayments?: boolean,
    readonly hasGameCodes?: boolean,
    readonly hasDeliverables?: boolean,
    readonly hasDiscordChannel?: boolean,
    readonly description?: string,
    readonly gameCodes?: OpportunityGameCode,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    readonly event?: any,
    readonly contentSubmission?: OpportunityContentSubmission,
    readonly discordChannelId?: string,
    readonly perks?: Array<Perk>,
    readonly hasAttachments?: boolean,
    readonly attachmentsUrl?: string
  ) {
    if (gameCodes !== null) {
      this.platformOptions = this.gameCodes.platformOptions();
      this.platformRegionOptions = this.gameCodes.platformRegionOptions();
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static fromApi(data: any): OpportunityWithPerksAndContentSubmission {
    return new OpportunityWithPerksAndContentSubmission(
      data.id,
      data.title,
      data.registrationPeriod,
      data.heroImage,
      data.backgroundImage,
      data.gameTitle,
      data.hasEvent,
      data.hasPayments,
      data.hasGameCodes,
      data.hasDeliverables,
      data.hasDiscordChannel,
      data.description,
      data.gameCodes ? OpportunityGameCode.fromApi(data.gameCodes) : null,
      data.event,
      data.contentSubmission,
      data.discordChannelId,
      data.perks,
      data.hasAttachments,
      data.attachmentsUrl
    );
  }
}
