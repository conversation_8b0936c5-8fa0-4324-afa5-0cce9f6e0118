import EAToken from "./EAToken";
import BadCredentials from "../authentication/BadCredentials";
import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import Identity from "../authentication/Identity";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

/**
 * @deprecated
 */
@Service()
class AuthenticationProvider {
  constructor(@Inject("operationsClient") private readonly client: TraceableHttpClient) {}

  async authenticate(token: EAToken, redirectUrl: URL): Promise<void> {
    if (token.code() === null) {
      throw new BadCredentials("Cannot authenticate a user without a code");
    }

    const identity = (await this.client.get("/v2/identities", {
      query: { code: token.code(), redirectUri: redirectUrl.toString() }
    })) as AxiosResponse;
    token.setIdentity(
      new Identity(
        identity.data.nucleusId,
        identity.data.defaultGamerTag,
        identity.data.dateOfBirth,
        identity.data.originEmail,
        identity.data.countryCode,
        identity.data.languageCode,
        identity.data.locale
      )
    );
  }
}

export default AuthenticationProvider;
