import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { NotificationPageLabels } from "@src/contentManagement/NotificationsPageMapper";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type DashboardProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
  FLAG_CONTENT_WITH_FINAL_REMARK: boolean;
  locale: string;
  showInitialMessage: boolean;
  pageLabels: NotificationPageLabels;
};

export default class DashboardPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<DashboardProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<DashboardProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "notifications"
    )) as NotificationPageLabels;
    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : false;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        locale: this.currentLocale,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "my-content",
          "notifications",
          "connect-accounts",
          "opportunities"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        FLAG_CONTENT_WITH_FINAL_REMARK: featureFlags.isContentWithFinalRemarksEnabled(),
        showInitialMessage,
        pageLabels
      }
    };
  }
}
