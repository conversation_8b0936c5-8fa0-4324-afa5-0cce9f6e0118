import { Controller, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import type { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { Inject, Service } from "typedi";
import config from "config";
import { NextApiResponse } from "next";
import { DEFAULT_LOCALE, getLocale } from "../../utils";

/**
 * @deprecated
 */
@Service()
class CloseSessionController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const locale = req.cookies.NEXT_LOCALE || getLocale(req.headers, config.SUPPORTED_LOCALES);
    const urlLocale = `${(locale !== DEFAULT_LOCALE && "/" + locale) || "/"}`;
    req.session.destroy(() => {
      res.setHeader("Location", encodeURI(`${urlLocale}`));
      res.status(302);
      res.end();
    });
  }
}

export default CloseSessionController;
