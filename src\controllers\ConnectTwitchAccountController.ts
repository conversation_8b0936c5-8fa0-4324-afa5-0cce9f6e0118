import { Inject, Service } from "typedi";
import ConnectedAccountsHttpClient from "../accounts/ConnectedAccountsHttpClient";
import { NextApiResponse } from "next";
import ConnectedAccountCredentials from "../channels/ConnectedAccountCredentials";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "../../config";

@Service()
class ConnectTwitchAccountController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly connectedAccounts: ConnectedAccountsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const isInterestedCreator = config.FLAG_PER_PROGRAM_PROFILE
      ? this.identity(req).type === "INTERESTED_CREATOR"
      : this.hasSession(req, "nucleusId");
    const code = this.query(req, "code") as string;
    let credentials: ConnectedAccountCredentials;

    if (isInterestedCreator) {
      const nucleusId = config.FLAG_PER_PROGRAM_PROFILE
        ? this.identity(req).nucleusId
        : (this.session(req, "nucleusId") as number);
      credentials = ConnectedAccountCredentials.forInterestedCreator(nucleusId, code);
    } else {
      const creator = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
      credentials = ConnectedAccountCredentials.forCreator(creator.id, code);
    }

    await this.connectedAccounts.connectTwitchAccount(credentials);

    await this.addToSession(req, "accountType", "Twitch");

    this.html(res, "<script>window.close();</script>");
  }
}

export default ConnectTwitchAccountController;
