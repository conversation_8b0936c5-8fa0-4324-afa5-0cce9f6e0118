import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ContentManagementService from "@src/api/services/ContentManagementService";
import ApiContainer from "@src/ApiContainer";
import NotificationsPagePropsController from "@src/serverprops/controllers/NotificationsPagePropsController";
import config from "config";

const notificationsProps = (locale: string) =>
  serverPropsControllerFactory(
    new NotificationsPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default notificationsProps;
