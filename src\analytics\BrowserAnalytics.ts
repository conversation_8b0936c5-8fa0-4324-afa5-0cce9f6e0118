import { ampli, Ampli } from "../../analytics/browser/src/ampli";
import { OpportunityWithDeliverables } from "../api/services/OpportunityService";
import { CreatorWithPayableStatusProfile } from "../api/services/CreatorsService";
import User from "../authentication/User";
import { DeliverableFormat } from "../../components/pages/content-submission/ContentDeliverablesTab";
import { Identity } from "@eait-playerexp-cn/identity-types";

type CreatorIdentity = {
  locale: string;
};

type CreatorPlatforms = CreatorIdentity & {
  primaryPlatform: string;
  secondaryPlatforms: string[];
};

type CommunicationPreferences = CreatorIdentity & {
  contentLanguages: string;
};

type FooterLink = CreatorIdentity & { url: string };

type ContinuedApplication = CreatorIdentity & { creatorTypes?: string[]; page: string; finalStep: boolean };

type OpportunityFlow = CreatorIdentity & {
  opportunity: OpportunityWithDeliverables;
  deliverableTitle?: string;
  deliverableType?: string;
};

type ContentSubmissionErrorMessage = OpportunityFlow & {
  errorCode: string;
  errorMessage: string;
  submissionType?: DeliverableFormat;
};

type CancelledJoinOpportunityFlow = OpportunityFlow & {
  pageAbandoned: string;
};

type SubmittedWebsiteURL = OpportunityFlow & {
  contentType: string;
  websiteDomain: string;
};

type SubmittedFileUpload = OpportunityFlow & {
  contentType: string;
  fileExtension: string;
  fileSize: number;
};

type SubmittedSocialContent = OpportunityFlow & {
  accountType: string;
};

type ConnectedNewSocialAccount = CreatorIdentity & {
  accountType: string;
  deliverableTitle?: string;
  deliverableType?: string;
};

type CheckedApplicationStatus = CreatorIdentity & {
  status: "Accepted" | "Rejected" | "Pending";
};

type CreatorApplication = CreatorIdentity & {
  page: string;
};

type CreatorProfileUpdate = CreatorIdentity & {
  creator: CreatorWithPayableStatusProfile;
};

type CreatorTypesUpdate = CreatorProfileUpdate & {
  selectedCreatorTypes?: string[];
};

type WithLabel = { label: string };

type CreatorPlatformsUpdate = CreatorProfileUpdate & {
  selectedPlatforms?: WithLabel[];
};

type CanceledOnboarding = CreatorIdentity & {
  page: string;
};

type TermsAndConditions = CreatorIdentity & {
  accepted: boolean;
};

type AppliedDateRangeFilter = CreatorIdentity & {
  selectedDateRange: string;
};

type AppliedPaymentStatusFilter = CreatorIdentity & {
  selectedPaymentStatus: string;
};

type AppliedPaymentTypeFilter = CreatorIdentity & {
  selectedPaymentType: string;
};

type AppliedAllPaymentFilters = AppliedDateRangeFilter & AppliedPaymentStatusFilter & AppliedPaymentTypeFilter;

type RemovedPaymentFilter = CreatorIdentity & {
  removedFilteredValue: string;
  removedFilterType: string;
};

export type CreatorType = "CREATOR" | "INTERESTED_CREATOR";

export type AuthenticatedUser = {
  analyticsId: string | undefined;
  needsMigration?: boolean;
  username: string;
  status?: string;
  avatar?: string;
  tier?: string;
  isPayable?: boolean;
  type?: CreatorType;
  isFlagged?: boolean;
  programs?: string[];
  creatorCode?: string;
};

export type InitialInterestedCreator = {
  analyticsId?: string | undefined;
  nucleusId: number | string;
  defaultGamerTag: string;
  originEmail: string;
  dateOfBirth: string;
  username?: string;
};

export class AuthenticatedUserFactory {
  static fromIdentity(identity: Identity, defaultAvatar: string, program: string): AuthenticatedUser {
    return {
      analyticsId: identity.analyticsId(),
      needsMigration: identity.needsMigration,
      username: identity.username,
      status: identity.statusFor(program),
      avatar: identity.avatar || defaultAvatar,
      tier: identity.tier ?? null,
      isPayable: identity.payable ?? false,
      isFlagged: identity.flagged ?? false,
      programs: identity.programs.map((program) => program.code) || [],
      creatorCode: identity.creatorCode || null,
      type: identity.type
    };
  }

  /**
   * @deprecated
   */
  static fromSession(user: User, FLAG_CREATORS_API_WITH_PROGRAM?: boolean): AuthenticatedUser {
    const authenticatedUser: AuthenticatedUser = {
      analyticsId: user.analyticsId,
      needsMigration: user.needsMigration,
      username: user.username,
      status: user.status,
      avatar: user.avatar,
      tier: user?.tier ?? null,
      isPayable: user?.isPayable ?? false,
      isFlagged: user?.isFlagged ?? null,
      type: "CREATOR"
    };

    if (FLAG_CREATORS_API_WITH_PROGRAM) authenticatedUser.programs = user.programs;

    return authenticatedUser;
  }

  static fromInterestedCreator(interestedCreator: InitialInterestedCreator | Identity): AuthenticatedUser {
    const isIdentity = interestedCreator instanceof Identity;
    return {
      analyticsId: isIdentity ? interestedCreator?.analyticsId() : interestedCreator?.analyticsId || null,
      username: isIdentity ? interestedCreator?.username : interestedCreator?.defaultGamerTag || null,
      type: "INTERESTED_CREATOR"
    };
  }
}

type MarketingPage = CreatorIdentity & {
  page: string;
};

class FileSize {
  private static UNITS = ["B", "KB", "MB", "GB", "TB"];

  constructor(private readonly sizeInBytes: number) {}

  format(): string {
    let index = 0;
    let size = this.sizeInBytes;

    while (size > 1024) {
      size /= 1024;
      index++;
    }
    return `${size.toFixed(2)} ${FileSize.UNITS[index]}`;
  }
}

class BrowserAnalytics {
  private readonly client: Ampli;
  private readonly user?: AuthenticatedUser;

  constructor(user?: AuthenticatedUser, client?: Ampli) {
    this.user = user;
    this.client = client || ampli;
  }

  startedOnboardingFlow(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.startedOnboardingFlow();
  }

  canceledOnboardingFlow(canceledOnboarding: CanceledOnboarding): void {
    this.client.identify(this.user?.analyticsId, { Locale: canceledOnboarding.locale });
    this.client.canceledOnboardingFlow({
      "Page Abandoned": canceledOnboarding.page
    });
  }

  confirmedFranchise(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Primary Franchise": profileUpdate.creator.preferredPrimaryFranchiseLabel(),
      "Secondary Franchise": profileUpdate.creator.preferredSecondaryFranchisesLabels()
    });
    this.client.confirmedFranchise({
      "Primary Franchise": profileUpdate.creator.preferredPrimaryFranchiseLabel(),
      "Secondary Franchise": profileUpdate.creator.preferredSecondaryFranchisesLabels()
    });
  }

  confirmedCreatorType(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Creator Types": profileUpdate.creator.creatorTypes
    });
    this.client.confirmedCreatorType({ "Selected Creator Types": profileUpdate.creator.creatorTypes });
  }

  confirmedSocialMediaChannel(creatorAccounts: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorAccounts.locale,
      "Connected Social Accounts": creatorAccounts.creator.socialAccountTypes()
    });
    this.client.confirmedSocialMediaChannel({
      "Connected Social Accounts": creatorAccounts.creator.socialAccountTypes()
    });
  }

  confirmedPlatform(creatorPlatforms: CreatorPlatforms): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorPlatforms.locale,
      "Primary Platform": creatorPlatforms.primaryPlatform,
      "Secondary Platform": creatorPlatforms.secondaryPlatforms
    });
    this.client.confirmedPlatform({
      "Primary Platform": creatorPlatforms.primaryPlatform,
      "Secondary Platform": creatorPlatforms.secondaryPlatforms
    });
  }

  confirmedCommunicationPreferences(communicationPreferences: CommunicationPreferences): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: communicationPreferences.locale,
      "Content Languages": communicationPreferences.contentLanguages
    });
    this.client.confirmedCommunicationPreferences();
  }

  signedTermsAndConditions(termsAndConditions: TermsAndConditions): void {
    this.client.identify(this.user?.analyticsId, { Locale: termsAndConditions.locale });
    this.client.signedTermsAndConditions({ "Agreed to T&C's": termsAndConditions.accepted });
  }

  completedOnboardingFlow(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.completedOnboardingFlow();
  }

  updatedCreatorTypesInProfile(profileUpdate: CreatorTypesUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Creator Types": profileUpdate.selectedCreatorTypes
    });
    this.client.updatedCreatorTypesInProfile({
      "Added Creator Types": profileUpdate.creator.addedCreatorTypes(profileUpdate.selectedCreatorTypes || []),
      "Removed Creator Types": profileUpdate.creator.removedCreatorTypes(profileUpdate.selectedCreatorTypes || [])
    });
  }

  updatedPrimaryFranchise(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Primary Franchise": profileUpdate.creator.preferredPrimaryFranchiseLabel()
    });
    this.client.updatedPrimaryFranchise({
      "Selected Franchise(s)": [profileUpdate.creator.preferredPrimaryFranchiseLabel()]
    });
  }

  updatedSecondaryFranchises(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Secondary Franchise": profileUpdate.creator.preferredSecondaryFranchisesLabels()
    });
    this.client.updatedSecondaryFranchises({
      "Selected Franchise(s)": profileUpdate.creator.preferredSecondaryFranchisesLabels()
    });
  }

  updatedPrimaryPlatformInProfile(profileUpdate: CreatorProfileUpdate): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Primary Platform": profileUpdate.creator.preferredPrimaryPlatformLabel()
    });
    this.client.updatedPrimaryPlatformInProfile({
      "Selected Platform(s)": [profileUpdate.creator.preferredPrimaryPlatformLabel()]
    });
  }

  updatedSecondaryPlatformsInProfile(profileUpdate: CreatorPlatformsUpdate): void {
    const platforms = profileUpdate.selectedPlatforms.map((platform) => platform.label);
    this.client.identify(this.user?.analyticsId, {
      Locale: profileUpdate.locale,
      "Secondary Platform": platforms
    });
    this.client.updatedSecondaryPlatformsInProfile({
      "Removed Platforms": profileUpdate.creator.removedSecondaryPlatforms(platforms),
      "Selected Platform(s)": profileUpdate.creator.addedSecondaryPlatforms(platforms)
    });
  }

  updatedBasicInformation(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: identity.locale
    });
    this.client.updatedBasicInformation();
  }

  viewedMarketingPage(marketingPage: MarketingPage): void {
    this.client.identify(this.user?.analyticsId, { Locale: marketingPage.locale });
    this.client.viewedMarketingPage({ "Page Displayed": marketingPage.page });
  }

  clickedFooterLink(footerLink: FooterLink): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: footerLink.locale
    });
    this.client.clickedFooterLink({
      "Link Clicked": footerLink.url
    });
  }

  signedOutOfCreatorNetwork(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: identity.locale
    });
    this.client.signedOutOfCreatorNetwork();
  }

  visitedMyProfile(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: identity.locale
    });
    this.client.visitedMyProfile();
  }

  viewedOpportunityDetails(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.viewedOpportunityDetails({
      "Opportunity Franchise": opportunityFlow.opportunity.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity.id,
      "Opportunity Name": opportunityFlow.opportunity.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  startedJoinOpportunityFlow(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.startedJoinOpportunityFlow({
      "Opportunity Franchise": opportunityFlow.opportunity.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity.id,
      "Opportunity Name": opportunityFlow.opportunity.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  continuedJoinOpportunityFlow(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.continuedJoinOpportunityFlow({
      "Opportunity Franchise": opportunityFlow.opportunity.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity.id,
      "Opportunity Name": opportunityFlow.opportunity.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  completedJoinOpportunityFlow(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.completedJoinOpportunityFlow({
      "Opportunity Franchise": opportunityFlow.opportunity?.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity?.id,
      "Opportunity Name": opportunityFlow.opportunity?.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  cancelledJoinOpportunityFlow(cancelledJoinOpportunityFlow: CancelledJoinOpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: cancelledJoinOpportunityFlow.locale
    });
    this.client.cancelledJoinOpportunityFlow({
      "Opportunity Franchise": cancelledJoinOpportunityFlow.opportunity.gameTitle,
      "Opportunity ID": cancelledJoinOpportunityFlow.opportunity.id,
      "Opportunity Name": cancelledJoinOpportunityFlow.opportunity.title,
      "Opportunity Perks": cancelledJoinOpportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": cancelledJoinOpportunityFlow.opportunity.types(),
      "Opportunity Platform": cancelledJoinOpportunityFlow.opportunity.gameCodesPlatforms(),
      "Opportunity Visibility": cancelledJoinOpportunityFlow.opportunity.visibility,
      "Page Abandoned": cancelledJoinOpportunityFlow.pageAbandoned
    });
  }

  acceptedOpportunityInvitation(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.acceptedOpportunityInvitation({
      "Opportunity Franchise": opportunityFlow.opportunity.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity.id,
      "Opportunity Name": opportunityFlow.opportunity.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  declinedOpportunityInvitation(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.declinedOpportunityInvitation({
      "Opportunity Franchise": opportunityFlow.opportunity.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity.id,
      "Opportunity Name": opportunityFlow.opportunity.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  clickedEmailPocLink(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.clickedEmailPocLink();
  }

  emailSentToPoc(identity: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, { Locale: identity.locale });
    this.client.emailSentToPoc();
  }

  checkedApplicationStatus(applicationStatus: CheckedApplicationStatus): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: applicationStatus.locale
    });
    this.client.checkedApplicationStatus({ "Application Status": applicationStatus.status });
  }

  startedCreatorApplication(creatorApplication: CreatorApplication): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorApplication.locale,
      Type: this.user?.type,
      Status: this.user?.status
    });
    this.client.startedCreatorApplication({ "Application Page": creatorApplication.page });
  }

  continuedCreatorApplication(continuedApplication: ContinuedApplication): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: continuedApplication.locale,
      "Creator Types": continuedApplication.creatorTypes,
      Type: this.user?.type,
      Status: this.user?.status
    });
    this.client.continuedCreatorApplication({
      "Application Page": continuedApplication.page,
      "Final Step": continuedApplication.finalStep
    });
  }

  cancelledCreatorApplication(creatorApplication: CreatorApplication): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: creatorApplication.locale,
      Type: this.user?.type,
      Status: this.user?.status
    });
    this.client.cancelledCreatorApplication({ "Application Page": creatorApplication.page });
  }

  submittedWebsiteUrl(submittedWebsiteURL: SubmittedWebsiteURL): void {
    this.client.identify(this.user?.analyticsId, { Locale: submittedWebsiteURL.locale });
    this.client.submittedWebsiteUrl({
      "Content Type": submittedWebsiteURL.contentType,
      "Opportunity Franchise": submittedWebsiteURL.opportunity?.gameTitle,
      "Opportunity ID": submittedWebsiteURL.opportunity?.id,
      "Opportunity Name": submittedWebsiteURL.opportunity?.title,
      "Opportunity Perks": submittedWebsiteURL.opportunity.perksCodes(),
      "Opportunity Type": submittedWebsiteURL.opportunity.types(),
      "Opportunity Platform": submittedWebsiteURL.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": submittedWebsiteURL.opportunity.visibility,
      "Website Domain Name": submittedWebsiteURL.websiteDomain,
      "Deliverable Title": submittedWebsiteURL.deliverableTitle,
      "Deliverable Type": submittedWebsiteURL.deliverableType
    });
  }

  submittedFileUpload(submittedFileUpload: SubmittedFileUpload): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: submittedFileUpload.locale
    });
    this.client.submittedFileUpload({
      "Content Type": submittedFileUpload.contentType,
      "File Extension": submittedFileUpload.fileExtension,
      "File Size": new FileSize(submittedFileUpload.fileSize).format(),
      "Opportunity Franchise": submittedFileUpload.opportunity?.gameTitle,
      "Opportunity ID": submittedFileUpload.opportunity?.id,
      "Opportunity Name": submittedFileUpload.opportunity?.title,
      "Opportunity Perks": submittedFileUpload.opportunity.perksCodes(),
      "Opportunity Type": submittedFileUpload.opportunity.types(),
      "Opportunity Platform": submittedFileUpload.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": submittedFileUpload.opportunity.visibility,
      "Deliverable Title": submittedFileUpload.deliverableTitle,
      "Deliverable Type": submittedFileUpload.deliverableType
    });
  }

  submittedSocialContent(submittedSocialContent: SubmittedSocialContent): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: submittedSocialContent.locale
    });
    this.client.submittedSocialContent({
      "Social Channel Type": submittedSocialContent.accountType,
      "Opportunity Franchise": submittedSocialContent.opportunity?.gameTitle,
      "Opportunity ID": submittedSocialContent.opportunity?.id,
      "Opportunity Name": submittedSocialContent.opportunity?.title,
      "Opportunity Perks": submittedSocialContent.opportunity.perksCodes(),
      "Opportunity Type": submittedSocialContent.opportunity.types(),
      "Opportunity Platform": submittedSocialContent.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": submittedSocialContent.opportunity.visibility,
      "Deliverable Title": submittedSocialContent.deliverableTitle,
      "Deliverable Type": submittedSocialContent.deliverableType
    });
  }

  connectedNewSocialAccount(connectedNewSocialAccount: ConnectedNewSocialAccount): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: connectedNewSocialAccount.locale
    });
    this.client.connectedNewSocialAccount({
      "Social Channel Type": connectedNewSocialAccount.accountType,
      "Deliverable Title": connectedNewSocialAccount.deliverableTitle,
      "Deliverable Type": connectedNewSocialAccount.deliverableType
    });
  }

  startedContentSubmissionFlow(contentSubmissionFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: contentSubmissionFlow.locale
    });

    this.client.startedContentSubmissionFlow({
      "Opportunity Franchise": contentSubmissionFlow.opportunity?.gameTitle,
      "Opportunity ID": contentSubmissionFlow.opportunity?.id,
      "Opportunity Name": contentSubmissionFlow.opportunity?.title,
      "Opportunity Perks": contentSubmissionFlow.opportunity.perksCodes(),
      "Opportunity Platform": contentSubmissionFlow.opportunity.gameCodesPlatforms(),
      "Opportunity Type": contentSubmissionFlow.opportunity.types(),
      "Opportunity Visibility": contentSubmissionFlow.opportunity.visibility
    });
  }

  receivedContentSubmissionErrorMessage(contentSubmissionErrorMessage: ContentSubmissionErrorMessage): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: contentSubmissionErrorMessage.locale
    });

    this.client.receivedContentSubmissionErrorMessage({
      "Opportunity Franchise": contentSubmissionErrorMessage.opportunity?.gameTitle,
      "Opportunity ID": contentSubmissionErrorMessage.opportunity?.id,
      "Opportunity Name": contentSubmissionErrorMessage.opportunity?.title,
      "Opportunity Perks": contentSubmissionErrorMessage.opportunity.perksCodes(),
      "Opportunity Platform": contentSubmissionErrorMessage.opportunity.gameCodesPlatforms(),
      "Opportunity Type": contentSubmissionErrorMessage.opportunity.types(),
      "Opportunity Visibility": contentSubmissionErrorMessage.opportunity.visibility,
      "Deliverable Title": contentSubmissionErrorMessage.deliverableTitle,
      "Error Code": contentSubmissionErrorMessage.errorCode,
      "Error Message": contentSubmissionErrorMessage.errorMessage,
      "Type of Content Submission": contentSubmissionErrorMessage.submissionType,
      "Deliverable Type": contentSubmissionErrorMessage.deliverableType
    });
  }

  /* Creator Wallet Events */
  clickedPaymentInformationInMyProfile(clickedPaymentInformationInMyProfile: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentInformationInMyProfile.locale
    });
    this.client.clickedPaymentInformationInMyProfile();
  }

  clickedPaidOpportunitiesWhenThereIsNoTransaction(
    clickedPaidOpportunitiesWhenThereIsNoTransaction: CreatorIdentity
  ): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaidOpportunitiesWhenThereIsNoTransaction.locale
    });
    this.client.clickedPaidOpportunitiesWhenThereIsNoTransaction();
  }

  clickedPaymentDetailsIncompleteTooltip(clickedPaymentDetailsIncompleteTooltip: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentDetailsIncompleteTooltip.locale
    });
    this.client.clickedPaymentDetailsIncompleteTooltip();
  }

  clickedPaymentDetailsIncompleteHelperBanner(clickedPaymentDetailsIncompleteHelperBanner: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentDetailsIncompleteHelperBanner.locale
    });
    this.client.clickedPaymentDetailsIncompleteHelperBanner();
  }

  clickedPaymentSettingsTab(clickedPaymentSettingsTab: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedPaymentSettingsTab.locale
    });
    this.client.clickedPaymentSettingsTab();
  }

  clickedOpportunityDescription(clickedOpportunityDescription: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: clickedOpportunityDescription.locale
    });
    this.client.clickedOpportunityDescription();
  }

  downloadedPaymentContract(downloadedPaymentContract: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: downloadedPaymentContract.locale
    });
    this.client.downloadedPaymentContract();
  }

  openedPaymentsFiltersForm(openedPaymentsFiltersForm: CreatorIdentity): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: openedPaymentsFiltersForm.locale
    });
    this.client.openedPaymentsFiltersForm();
  }

  appliedDateRangeFilter(appliedDateRangeFilter: AppliedDateRangeFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedDateRangeFilter.locale
    });
    this.client.appliedDateRangeFilter({
      "Selected Date Range Option": appliedDateRangeFilter.selectedDateRange
    });
  }

  appliedPaymentStatusFilter(appliedPaymentStatusFilter: AppliedPaymentStatusFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedPaymentStatusFilter.locale
    });
    this.client.appliedPaymentStatusFilter({
      "Payment Status Selected": appliedPaymentStatusFilter.selectedPaymentStatus
    });
  }

  appliedPaymentTypeFilter(appliedPaymentTypeFilter: AppliedPaymentTypeFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedPaymentTypeFilter.locale
    });
    this.client.appliedPaymentTypeFilter({
      "Payment Type Selected": appliedPaymentTypeFilter.selectedPaymentType
    });
  }

  appliedAllPaymentFilters(appliedAllPaymentFilters: AppliedAllPaymentFilters): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: appliedAllPaymentFilters.locale
    });
    this.client.appliedAllPaymentFilters({
      "Payment Status Selected": appliedAllPaymentFilters.selectedPaymentStatus,
      "Payment Type Selected": appliedAllPaymentFilters.selectedPaymentType,
      "Selected Date Range Option": appliedAllPaymentFilters.selectedDateRange
    });
  }

  removedPaymentFilter(removedPaymentFilter: RemovedPaymentFilter): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: removedPaymentFilter.locale
    });

    const FILTER_TYPE_AMPLI_MAPPING = {
      range: "Date Range",
      status: "Payment Status",
      opportunityType: "Opportunity Type"
    };

    this.client.removedPaymentFilter({
      "Removed Filtered Value": removedPaymentFilter.removedFilteredValue,
      "Removed Filter Type": FILTER_TYPE_AMPLI_MAPPING[removedPaymentFilter.removedFilterType]
    });
  }

  clickedDownloadAttachment(opportunityFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: opportunityFlow.locale
    });
    this.client.clickedDownloadAttachment({
      "Opportunity Franchise": opportunityFlow.opportunity.gameTitle,
      "Opportunity ID": opportunityFlow.opportunity.id,
      "Opportunity Name": opportunityFlow.opportunity.title,
      "Opportunity Perks": opportunityFlow.opportunity.perksCodes(),
      "Opportunity Type": opportunityFlow.opportunity.types(),
      "Opportunity Platform": opportunityFlow.opportunity?.gameCodesPlatforms(),
      "Opportunity Visibility": opportunityFlow.opportunity.visibility
    });
  }

  clickedDeliverablesTab(contentSubmissionFlow: OpportunityFlow): void {
    this.client.identify(this.user?.analyticsId, {
      Locale: contentSubmissionFlow.locale
    });

    this.client.clickedDeliverablesTab({
      "Opportunity Franchise": contentSubmissionFlow.opportunity?.gameTitle,
      "Opportunity ID": contentSubmissionFlow.opportunity?.id,
      "Opportunity Name": contentSubmissionFlow.opportunity?.title,
      "Opportunity Perks": contentSubmissionFlow.opportunity.perksCodes(),
      "Opportunity Platform": contentSubmissionFlow.opportunity.gameCodesPlatforms(),
      "Opportunity Type": contentSubmissionFlow.opportunity.types(),
      "Opportunity Visibility": contentSubmissionFlow.opportunity.visibility
    });
  }
}

export default BrowserAnalytics;
