@import "./opportunities/submit-content.css";
.opportunity-perks-container {
  @apply min-h-screen bg-page pl-meas0 font-text-regular text-white;
}
.opportunity-perks-full-screen {
  @apply m-auto w-full;
}
.opportunity-perks-full-screen > .tab-container {
  @apply mt-meas12;
}
.opportunity-perks-back-nav {
  @apply mr-[40px] flex pt-meas20 font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large xl:ml-[-25px];
}
.opportunity-perks-back-nav > span:last-child {
  @apply ml-meas4;
}
.opportunity-perks-back-nav .icon {
  @apply h-meas9 w-meas9;
}
.opportunity-perks-header-container-with-sticky {
  @apply lg:fixed lg:top-[3.8rem] lg:z-[1] lg:flex lg:h-[78px] lg:w-full lg:min-w-[1024px] lg:content-center lg:items-center lg:justify-center lg:border-b lg:border-solid lg:border-navy-60 lg:bg-page  lg:px-meas24 lg:py-meas8  xl:px-meas0;
  animation: slideDown 0.5s forwards;
}
.opportunity-perks-header-container-with-sticky-slide-up {
  @apply lg:fixed lg:top-[3.8rem] lg:z-[1] lg:flex lg:h-[78px] lg:w-full lg:min-w-[1024px] lg:content-center lg:items-center lg:justify-center lg:border-b lg:border-solid lg:border-navy-60 lg:bg-page  lg:px-meas24 lg:py-meas8  xl:px-meas0;
  animation: slideUp 0.5s forwards;
}

.opportunity-headerv2-setting .opportunity-card-status-pill-invited .opportunity-card-status-pill-icon-invited,
.opportunity-headerv2-setting .opportunity-card-status-pill-joined .opportunity-card-status-pill-icon-joined,
.opportunity-headerv2-setting .opportunity-card-status-pill-open .opportunity-card-status-pill-icon-open {
  @apply mb-[1.2px];
}
@keyframes slideUp {
  from {
    top: 3.8rem;
  }
  to {
    top: 0rem;
    display: none;
  }
}

@keyframes slideDown {
  from {
    top: 0px;
    display: none;
  }
  to {
    top: 3.8rem;
  }
}

.opportunity-tabs-action-buttons-container-with-sticky {
  @apply lg:flex lg:min-w-[1024px]  lg:justify-between lg:px-meas24 xl:px-meas0;
}
.opportunity-action-buttons-container-with-sticky {
  @apply w-auto lg:flex lg:flex-row lg:items-center;
}
.opportunity-tabs-container-with-sticky {
  @apply lg:w-auto;
}
.opportunity-card-registration-detail-with-sticky {
  @apply w-full text-right font-text-regular font-normal tracking-[0.8px] text-warning-50 lg:mr-meas8 lg:text-desktop-body-small lg:leading-5;
}
.opportunity-card-button-with-sticky {
  @apply lg:flex lg:justify-center;
}
.opportunity-card-button-with-status-open-sticky > button {
  @apply h-[46px] lg:w-auto lg:min-w-[195px];
}
.opportunity-card-button-with-sticky > button:first-child {
  @apply lg:ml-meas0 lg:h-[46px] lg:min-w-[156px];
}
.opportunity-card-button-with-sticky > button {
  @apply lg:ml-meas4 lg:h-[46px] lg:min-w-[156px];
}
.opportunity-perks-header-inner {
  @apply flex h-[100%] w-full max-w-[1024px] flex-col justify-between px-meas8 lg:mx-auto lg:max-w-[1024px] lg:px-meas24  xl:px-meas0;
}
.opportunity-card-registration-detail {
  @apply mb-meas6 mt-meas8 font-text-regular font-normal leading-4 tracking-[0.8px] text-warning-50 xs:text-mobile-caption1 lg:hidden xl:text-desktop-body-small xl:leading-5;
}
.opportunity-card-button-with-status-open > button {
  @apply h-[46px] xs:w-full md:w-auto md:min-w-[320px];
}
.opportunity-card-button {
  @apply flex;
}
.opportunity-card-button > button:first-child {
  @apply ml-meas0 h-[46px] xs:w-full md:w-auto md:min-w-[156px];
}
.opportunity-card-button > button {
  @apply ml-meas4 h-[46px] xs:w-full md:w-auto md:min-w-[156px];
}
.opportunity-tabs-action-buttons-container {
  @apply flex flex-col-reverse lg:flex lg:flex-row lg:items-end lg:justify-between;
}
.opportunity-tabs-container {
  @apply mt-meas10 lg:w-[100%] lg:border-b lg:border-solid lg:border-navy-60;
}
.opportunity-action-buttons-container {
  @apply lg:flex lg:flex-col lg:items-end  lg:justify-between lg:pl-meas20;
}
.opportunity-perks-headerv2-container {
  @apply flex flex-col items-center justify-center;
}
.opportunity-perks-header-mobile-or-tab-container {
  @apply relative mx-auto inline-block w-full max-w-[1024px];
}
.opportunity-perks-header-mobile-or-tab-container::after {
  @apply absolute h-[124%] w-full flex-shrink-0;
  content: "";
  bottom: -1px;
  left: 0;
  right: 0;
  background: var(--blur-overlay) no-repeat center;
  background-size: cover;
}
.opportunity-perks-header-mobile-or-tab {
  @apply block w-[100%] flex-shrink-0 bg-cover xs:h-[180px] md:h-[432px] lg:h-[576px];
}
@media (min-width: 1024px) {
  .opportunity-perks-header-mobile-or-tab-container::after {
    @apply absolute h-[576px] w-[1024px] flex-shrink-0;
    content: "";
    bottom: 0px;
    left: 0;
    right: 0;
    background: var(--blur-overlay) no-repeat center;
    background-size: cover;
  }
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
  .opportunity-tabs-container {
    @apply w-[100%] border-b border-solid border-navy-60;
  }
  .opportunity-action-buttons-container {
    @apply lg:flex lg:flex-col lg:items-start lg:justify-start lg:justify-between;
  }
}

@media only screen and (min-width: 320px) and (max-width: 768px) {
  .opportunity-tabs-container {
    @apply w-[100%] border-b border-solid border-navy-60;
  }
  .opportunity-action-buttons-container {
    @apply lg:flex lg:flex-col lg:items-start lg:justify-start lg:justify-between;
    text-align: start;
    width: 100%;
  }
}

@media screen and (min-width: 320px) {
  .opportunity-perks-header-container {
    background: var(--opportunity-header-background),
      linear-gradient(270deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.8) 100%),
      linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.79) 100%),
      linear-gradient(180deg, rgba(255, 71, 71, 0.85) 0%, rgba(42, 55, 141, 0.85) 100%);
    background-position: 96% 8%;
    background-repeat: no-repeat;
    background-size: 250px, contain, contain, contain;
  }
}

@media screen and (min-width: 768px) {
  .opportunity-perks-header-container {
    background: var(--opportunity-header-background),
      linear-gradient(270deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.8) 100%),
      linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.79) 100%),
      linear-gradient(180deg, rgba(255, 71, 71, 0.85) 0%, rgba(42, 55, 141, 0.85) 100%);
    background-position: 96% 8%;
    background-repeat: no-repeat;
    background-size: 450px, contain, contain, contain;
  }
}

@media screen and (min-width: 1024px) {
  .opportunity-perks-what-to-do-with-flag .opportunity-details-action-item-container {
    grid-template-columns: repeat(8, minmax(96px, 1fr));
  }
  .opportunity-perks-header-container {
    background: var(--opportunity-header-background),
      linear-gradient(270deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.8) 100%),
      linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.79) 100%),
      linear-gradient(180deg, rgba(255, 71, 71, 0.85) 0%, rgba(42, 55, 141, 0.85) 100%);
    background-position: 96% 8%;
    background-repeat: no-repeat;
    background-size: 500px, contain, contain, contain;
  }
}

@media screen and (min-width: 1440px) {
  .opportunity-perks-header-container {
    background: var(--opportunity-header-background),
      linear-gradient(270deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.8) 100%),
      linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 0.79) 100%),
      linear-gradient(180deg, rgba(255, 71, 71, 0.85) 0%, rgba(42, 55, 141, 0.85) 100%);
    background-position: 96% 8%;
    background-repeat: no-repeat;
    background-size: 850px, contain, contain, contain;
  }
}

.opportunity-perks-header {
  @apply mx-auto w-full max-w-[1280px] px-meas8 xl:max-w-[1280px];
}
.opportunity-perks-header-detail-container {
  @apply md:mb-meas10;
}
.opportunity-header-details {
  @apply ml-meas0 mt-meas12;
}
.opportunity-perks-overview-body {
  @apply mx-auto w-full max-w-[1150px] px-meas8 pb-meas38 pt-meas20 xl:max-w-[1180px];
}
.opportunity-perks-overview-body .view-all {
  @apply mt-meas0;
}
.opportunity-perks-overview-body-with-flag {
  @apply mb-meas34 mt-meas16 h-[100%] w-full max-w-[1024px] px-meas8 lg:mx-auto lg:max-w-[1024px] lg:px-meas24 xl:px-meas0;
}
/* Overriding css properties in opportunity-description and highlight-card-container components to sync with opportunity header ui */
.opportunity-perks-overview-body-with-flag .opportunity-description {
  @apply mt-meas0;
}
.opportunity-perks-overview-body-with-flag .highlight-card-container {
  @apply mb-meas10;
}
.highlight-carrd-container {
  @apply lg:w-[360px];
}
.opportunity-perks-hero-card-container {
  @apply flex flex-wrap md:flex-row;
}
.opportunity-perks-hero-image {
  @apply w-full rounded px-[inherit] lg:w-[600px] lg:px-meas0 xl:h-[480px] xl:w-[730px];
}
.opportunity-perks-hero-image-full-width {
  @apply w-auto flex-1 object-cover;
}
.opportunity-perks-details {
  @apply mx-meas0 mt-meas7 w-full md:mt-meas12 lg:ml-meas12 lg:mt-meas0 lg:w-auto;
}
.opportunity-perks-details-with-flag {
  @apply mx-meas0 w-full  lg:mt-meas0 lg:w-auto;
}
.opportunity-perks-content-submission-body {
  @apply mx-auto flex w-full max-w-[1150px] flex-wrap px-meas8 md:flex-row xl:max-w-[1180px];
}
.opportunity-perks-content-label {
  @apply pb-meas16;
}
.opportunity-perks-secondary-content-container {
  @apply mt-meas18 md:mt-meas26;
}
.opportunity-perks-secondary-content-title {
  @apply font-display-regular font-bold text-white xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.opportunity-perks-secondary-content-description {
  @apply mt-meas6  text-white xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.opportunity-perks-secondary-content-description ul {
  @apply list-disc pl-meas8;
}
.opportunity-perks-secondary-content-description ol {
  @apply list-decimal pl-meas8;
}
.opportunity-perks-additional-info-container {
  @apply mt-meas24;
}
.opportunity-perks-additional-info {
  @apply font-display-regular font-bold  xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.opportunity-perks-information-content {
  @apply mt-meas4 font-text-regular  xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.opportunity-perks-information-content ul {
  @apply list-disc pl-meas8;
}
.opportunity-perks-information-content ol {
  @apply list-decimal pl-meas8;
}
.opportunity-perks-content-submission-container {
  @apply flex w-full flex-col pt-meas20 md:pb-meas20  lg:flex-row xl:w-[780px] xl:pb-meas38;
}
.opportunity-perks-content-submission-card {
  @apply flex w-full flex-col pb-meas38 lg:flex-row xl:w-[360px] xl:pt-meas20;
}
.opportunity-perks-content-card-list-container {
  @apply w-full pb-meas12 lg:mr-meas12 lg:w-[600px] xl:w-[730px];
}
.opportunity-perks-content-todo-icon {
  @apply mr-meas2 mt-[2px] md:mt-meas0 md:self-center;
}
.opportunity-perks-content-todo-icon-enabled {
  @apply text-success-50;
}
.opportunity-perks-content-title-success-icon {
  @apply mr-meas2 mt-[2px] h-meas10 w-meas10 text-success-70 md:mt-meas0 md:self-center;
}
.opportunity-perks-content-block-signup-button {
  @apply m-auto inline-block md:min-w-[170px] xl:min-w-[260px];
}
.opportunity-perks-secondary-content-things-to-do-row {
  @apply mb-meas8 flex;
}
.opportunity-perks-secondary-content-things-to-do-row-hide {
  @apply mb-meas8 hidden;
}
.opportunity-perks-content-platforms {
  @apply flex w-[290px] flex-wrap md:w-[640px] xl:w-[790px];
}
.opportunity-perks-content-platform-card-container {
  @apply mr-meas9 mt-meas6 md:mt-meas10;
}
.opportunity-perks-content-platform-card {
  @apply flex h-[98px] w-[98px] items-center justify-center rounded-[2px] border-[0.5px] border-navy-60 bg-navy-80 text-gray-30;
}
.opportunity-perks-content-platform-image {
  @apply h-[49px] w-[49px];
}
.opportunity-perks-content-platform-card-label {
  @apply mt-meas4 w-[98px] text-center font-text-regular text-gray-10 xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.opportunity-perks-details-mobile {
  @apply lg:hidden;
  margin-left: -20px;
  margin-right: -20px;
}
.opportunity-perks-details-mobile .perks-card-container {
  @apply rounded-none;
}
.opportunity-perks-details .perks-card-container {
  @apply hidden lg:flex;
}
.opportunity-perks-what-to-do,
.opportunity-disclosure-policy {
  @apply mt-meas22 md:mt-[50px];
}
.opportunity-perks-free-game-code-details-container {
  @apply mt-meas20;
}
.opportunity-perks-content-dropdown-container {
  @apply mx-auto mb-meas16 flex min-h-[90px] w-full flex-col self-center rounded-lg border border-[#1E2497] p-meas9;
}

.opportunity-perks-content-submission-detail {
  @apply mt-meas4 h-meas8 w-full py-meas9 font-text-regular xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}

.opportunity-content-submitted-list {
  @apply my-meas10;
}

.opportunity-perks-details-no-left-spacing {
  @apply lg:ml-meas0;
}

.opportunity-creator-code {
  @apply mt-meas22;
}
.opportunity-creator-code-info-text {
  @apply mt-meas6 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}

.opportunity-perks-information-content a {
  @apply block overflow-hidden text-ellipsis whitespace-nowrap sm:w-[300px] md:w-[700px] lg:w-auto;
}
