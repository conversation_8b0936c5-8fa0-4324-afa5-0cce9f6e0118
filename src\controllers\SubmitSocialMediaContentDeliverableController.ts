import { NextApiResponse } from "next";
import SubmittedContentHttpClient from "../submittedContent/SubmittedContentHttpClient";
import { Inject, Service } from "typedi";
import config from "../../config";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class SubmitSocialMediaContentDeliverableController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly submitContent: SubmittedContentHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const participationId = req.body?.participationId;
    const socialMediaContent = req.body?.socialMediaContent;

    if (config.FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED) {
      await this.submitContent.saveSocialMediaContentWithInstagramMedia(participationId, socialMediaContent);
    } else await this.submitContent.saveSocialMediaContents(participationId, socialMediaContent);

    this.empty(res, HttpStatus.OK);
  }
}

export default SubmitSocialMediaContentDeliverableController;
