import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ConnectAccountsPagePropsController from "@src/serverprops/controllers/ConnectAccountsPagePropsController";
import config from "config";

const connectAccountsProps = (locale: string) =>
  serverPropsControllerFactory(
    new ConnectAccountsPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default connectAccountsProps;
