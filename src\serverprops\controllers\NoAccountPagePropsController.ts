import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import { Identity } from "@eait-playerexp-cn/identity-types";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { NoAccountLabels } from "@src/contentManagement/NoAccountPageMapper";
import { GetServerSidePropsResult } from "next";
import { NoAccountType } from "pages/interested-creators/no-account";
import featureFlags from "utils/feature-flags";

export default class NoAccountPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<NoAccountType>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<NoAccountType>> {
    const interestedCreator = this.hasSession(req, "noAccountCreator")
      ? (this.session(req, "noAccountCreator") as Identity)
      : null;

    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };

    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : null;

    const pageLabels = (await this.contents.getPageLabels(this.currentLocale, "noAccount")) as NoAccountLabels &
      ApplicationStartPageLabels &
      CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator),
        locale: this.currentLocale,
        showInitialMessage,
        pageLabels
      }
    };
  }
}
