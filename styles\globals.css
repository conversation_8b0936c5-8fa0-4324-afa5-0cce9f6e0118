@import "./vars.css";
@import "./footer.css";
@import "./common.css";
@import "./homepage.css";
@import "./header.css";
@import "./layout.css";
@import "./homepages/reward-hero.css";
@import "./components/join-opportunity-steps.css";
@import "./sign-up.css";
@import "./facebook-connect.css";
@import "./dashboard.css";
@import "./opportunities.css";
@import "./notifications.css";
@import "./my-content.css";
@import "./opportunity.css";
@import "./opportunity-with-perks.css";
@import "./profile/index.css";
@import "opportunities/criteria.css";
@import "opportunities/join-layout.css";
@import "../components/pages/content-submission/SocialMediaGuideModal.css";
@import "../components/pages/content-submission/ContentSubmissionModal.css";
@import "../components/pages/DisclosurePage.css";
@import "./faq.css";
@import "./common-components/interested-creator.css";
@import "../components/pages/interested-creators/CancelRegistrationModal.css";
@import "./trust-and-safety-guidelines.css";
@import "../components/header/TopNavBar.css";
@import "../components/pages/content-submission/SubmitSocialMediaContentForm.css";
@import "../components/pages/content-submission/ContentDeliverablesTab.css";
@import "../components/pages/payment-information/PaymentInformationPage.css";
@import "../components/pages/payment-information/PaymentDetailsTab/PaymentDetailsTab.css";
@import "../components/pages/payment-information/PaymentDetailsTab/TransactionHistory/TransactionHistory.css";
@import "../components/pages/payment-information/PaymentDetailsTab/TransactionHistory/CreatorCodeTooltip.css";
@import "../components/pages/payment-information/PaymentDetailsTab/PaymentsFilterForm.css";
@import "../components/rewards/EarlyGameAccess.css";
@import "../components/ToolTip.css";
@import "rc-tooltip/assets/bootstrap_white.css";
@import "../components/BuildInfo.css";
@import "../components/pages/MorePerksModal.css";
@import "../components/pages/SupportACreatorModal.css";
@import "../components/pages/GameCodeModal.css";
@import "../components/pages/EventDetailsModal.css";
@import "../components/upload/UpdateProfilePicture.css";
@import "../components/textarea/TextArea.css";

html,
body,
body > div:first-child,
div#__next,
div#__next > div:nth-child(2) {
  overflow-y: auto;
  background: #0d1042;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

/*Override Chrome Autofill Css*/

input:disabled:-webkit-autofill,
input:disabled:-webkit-autofill:hover,
input:disabled:-webkit-autofill:focus,
input:disabled:-webkit-autofill:active {
  transition: background-color 5000s;
  -webkit-text-fill-color: #fff !important;
  -webkit-background-clip: text;
  color: white !important;
}

.react-datepicker-wrapper {
  @apply w-full;
}

.react-datepicker {
  @apply font-display-regular;
}
