import "reflect-metadata";
import React, { ComponentType, useCallback } from "react";
import { useRouter } from "next/router";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody } from "@components/Layout";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "utils/feature-flags";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import ageRestrictionProps from "@src/serverprops/AgeRestrictionProps";
import { GetServerSidePropsResult } from "next";
import InterestedCreatorHeader from "@components/pages/interested-creators/InterestedCreatorHeader";
import session from "@src/middleware/Session";
import { addTelemetryInformation } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { AgeRestrictionPageLabels } from "@src/contentManagement/AgeRestrictionPageMapper";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

const AgeRestrictionPage: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/AgeRestrictionPage"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type AgeRestrictionProps = {
  runtimeConfiguration?: Record<string, unknown>;
  locale?: string;
  pageLabels: AgeRestrictionPageLabels & CommonPageLabels;
};

export default function AgeRestriction({ pageLabels }: AgeRestrictionProps): JSX.Element {
  const { ageRestrictionLabels, commonPageLabels } = pageLabels;
  const { creatorNetwork, close } = commonPageLabels;
  const router = useRouter();
  const logout = useCallback(() => {
    router.push("/api/logout");
  }, [router]);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} onClose={logout} />
          <div className="mg-bg"> </div>
          <AgeRestrictionPage
            labels={ageRestrictionLabels}
            onClose={logout}
            ageRestrictionBannerImage={"/img/home-header--980w-x-690h.png"}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(ageRestrictionProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<AgeRestrictionProps>;
  }

  await session(req, res, null);
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  await addTelemetryInformation(req, res, () => {});
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "ageRestriction");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      locale,
      pageLabels
    }
  };
};
