import { NextApiResponse } from "next";
import formidable from "formidable";
import { <PERSON>H<PERSON><PERSON> } from "next-connect";
import {
  NextApiRequestWithMultipartFile,
  NextApiRequestWithSession,
  NextMiddleware,
  UploadedFile
} from "@eait-playerexp-cn/server-kernel";

type MultipartDataParser = {
  parse: (
    req: NextApiRequestWithSession,
    callback: (
      error: Record<string, unknown>,
      fields: Record<string, unknown>,
      files: Record<string, UploadedFile>
    ) => void
  ) => unknown;
};

function parseMultipartData(req: NextApiRequestWithSession, middleware: MultipartDataParser) {
  return new Promise((resolve, reject) => {
    middleware.parse(req, (error, fields, files) => {
      if (error) {
        (req as NextApiRequestWithMultipartFile).error = error;
        return reject({ err: error });
      }
      (req as NextApiRequestWithMultipartFile).files = files;
      return resolve({ fields, files });
    });
  });
}
const parseUploadFiles: NextMiddleware<NextApiRequestWithSession, NextApiResponse, NextHandler> = async (
  req,
  _res,
  next
) => {
  await parseMultipartData(req, formidable({}));
  await next();
};

export default parseUploadFiles;
