export default function labelsInformation(t) {
  return {
    title: t("information:title"),
    interestedCreatorTitle: t("information:interestedCreatorTitle"),
    interestedUserDescription1: t("information:interestedUserDescription1"),
    interestedUserDescription2: t("information:interestedUserDescription2"),
    infoTitle: t("information:infoTitle"),
    infoSubTitle: t("information:infoSubTitle"),
    primaryPlatform: t("information:primaryPlatform"),
    platformPreferences: t("information:platformPreferences"),
    platformPreferencesTitle: t("information:platformPreferencesTitle"),
    secondaryPlatforms: t("information:secondaryPlatforms"),
    secondaryPlatformsTitle: t("information:secondaryPlatformsTitle"),
    modalConfirmationTitle: t("common:modalConfirmationTitle"),
    confirmationDesc1: t("common:confirmationDesc1"),
    confirmationDesc2: t("common:confirmationDesc2"),
    personalInformation: t("information:personalInformation"),
    mailingAddress: t("information:mailingAddress"),
    miscellaneous: t("information:miscellaneous"),
    creatorSince: t("information:creatorSince"),
    legalEntityType: t("information:legalEntityType"),
    legalEntityDescription: t("information:legalEntityDescription"),
    success: {
      updatedInformationHeader: t("information:success.updatedInformationHeader"),
      personalInformation: t("information:success.personalInformation"),
      mailingAddress: t("information:success.mailingAddress"),
      miscellaneous: t("information:success.miscellaneous"),
      legalEntityType: t("information:success.legalEntityType"),
      platformPreferences: t("information:success.platformPreferences")
    },
    labels: {
      none: t("information:labels.none"),
      preferredEmail: t("information:labels.preferredEmail"),
      firstName: t("information:labels.firstName"),
      lastName: t("information:labels.lastName"),
      EAID: t("information:labels.EAID"),
      EAEmail: t("information:labels.EAEmail"),
      dateOfBirth: t("information:labels.dateOfBirth"),
      country: t("information:labels.country"),
      street: t("information:labels.street"),
      addressLine2: t("information:labels.addressLine2"),
      city: t("information:labels.city"),
      state: t("information:labels.state"),
      zipCode: t("information:labels.zipCode"),
      primaryPlatform: t("information:labels.primaryPlatform"),
      tShirtSize: t("information:labels.tShirtSize"),
      hardwarePartners: t("information:labels.hardwarePartners"),
      entityType: t("information:labels.entityType"),
      individual: t("information:labels.individual"),
      business: t("information:labels.business"),
      businessName: t("information:labels.businessName"),
      legalAddressAsMailingAddress: t("information:labels.legalAddressAsMailingAddress"),
      contentMediaTitle: t("information:labels.contentMediaTitle"),
      contentMediaDescription: t("information:labels.contentMediaDescription"),
      contentUrl: t("information:labels.contentUrl"),
      contentFollowers: t("information:labels.contentFollowers"),
      contentUrlPlaceholder: t("information:labels.contentUrlPlaceholder"),
      contentFollowersPlaceholder: t("information:labels.contentFollowersPlaceholder"),
      contentLanguage: t("information:labels.contentLanguage"),
      selectCountry: t("information:labels.selectCountry"),
      connectSocialMediaAccountTitle: t("connect-accounts:title"),
      connectSocialMediaAccountDescription: t("connect-accounts:description"),
      additionalContentAndWebsiteTitle: t("information:labels.additionalContentAndWebsiteTitle"),
      additionalContentAndWebsiteDescription: t("information:labels.additionalContentAndWebsiteDescription"),
      additionalContentAndWebsiteLinks: t("information:labels.additionalContentAndWebsiteLinks"),
      websiteUrlLabel: t("information:labels.websiteUrlLabel"),
      additionalLinkPlaceholder: t("information:labels.additionalLinkPlaceholder")
    },
    messages: {
      firstName: t("information:messages.firstName"),
      firstNameTooLong: t("information:messages.firstNameTooLong"),
      lastName: t("information:messages.lastName"),
      lastNameTooLong: t("information:messages.lastNameTooLong"),
      ageMustBe18OrOlder: t("information:messages.ageMustBe18OrOlder"),
      dateOfBirth: t("information:messages.dateOfBirth"),
      dateOfBirthInvalid: t("information:messages.dateOfBirthInvalid"),
      country: t("information:messages.country"),
      street: t("information:messages.street"),
      streetTooLong: t("information:messages.streetTooLong"),
      city: t("information:messages.city"),
      cityTooLong: t("information:messages.cityTooLong"),
      state: t("information:messages.state"),
      stateTooLong: t("information:messages.stateTooLong"),
      zipCode: t("information:messages.zipCode"),
      zipCodeTooLong: t("information:messages.zipCodeTooLong"),
      primaryPlatform: t("information:messages.primaryPlatform"),
      secondaryPlatforms: t("information:messages.secondaryPlatforms"),
      tShirtSize: t("information:messages.tShirtSize"),
      hardwarePartners: t("information:messages.hardwarePartners"),
      entityType: t("information:messages.entityType"),
      businessName: t("information:messages.businessName"),
      businessNameTooLong: t("information:messages.businessNameTooLong"),
      email: t("information:messages.email"),
      emailTooLong: t("information:messages.emailTooLong"),
      emailInvalid: t("information:messages.emailInvalid"),
      url: t("information:messages.url"),
      invalidUrl: t("information:messages.invalidUrl"),
      duplicateUrl: t("information:messages.duplicateUrl"),
      urlScanFailed: t("information:messages.urlScanFailed"),
      followersMaxLength: t("information:messages.followersMaxLength")
    },
    profilePicture: {
      title: t("information:profilePicture.title"),
      message: t("information:profilePicture.message"),
      termsAndConditionsFirst: t("information:profilePicture.termsAndConditionsFirst"),
      termsAndConditionsMiddle: t("information:profilePicture.termsAndConditionsMiddle"),
      termsAndConditionsLast: t("information:profilePicture.termsAndConditionsLast"),
      avatarRequired: t("information:profilePicture.avatarRequired"),
      avatarInvalid: t("information:profilePicture.avatarInvalid"),
      avatarMoreThanLimit: t("information:profilePicture.avatarMoreThanLimit")
    },
    info: {
      businessName: t("information:info.businessName")
    }
  };
}
