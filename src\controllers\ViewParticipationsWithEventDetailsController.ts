import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import OpportunitiesHttpClient, { OpportunityCriteriaWithProgram } from "../opportunities/OpportunitiesHttpClient";
import OpportunityCriteria from "../actions/Dashboard/OpportunityCriteria";
import OpportunitiesWithActivationWindowPage from "../opportunities/OpportunitiesWithActivationWindowPage";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ViewParticipationsWithEventDetailsController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly opportunities: OpportunitiesHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    let opportunities: OpportunitiesWithActivationWindowPage;
    if (config.FLAG_OPPORTUNITIES_PER_PROGRAM) {
      const criteria = { ...req.query, programCode: config.PROGRAM_CODE };
      opportunities = await this.opportunities.matchingWithEventDetailsWithProgram(
        user.id as string,
        criteria as OpportunityCriteriaWithProgram
      );
    } else {
      opportunities = await this.opportunities.matchingWithEventDetails(
        user.id as string,
        req.query as unknown as OpportunityCriteria
      );
    }

    this.json(res, opportunities);
  }
}

export default ViewParticipationsWithEventDetailsController;
