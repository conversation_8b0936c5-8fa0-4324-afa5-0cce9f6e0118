import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { RequestHandlerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { GetServerSidePropsResult } from "next";
import { StartProps } from "pages/interested-creators/start";
import featureFlags from "utils/feature-flags";

export default class ApplicationStartPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<StartProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<StartProps>> {
    if (!featureFlags.isInterestedCreatorFlowEnabled()) {
      return { notFound: true };
    }
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "applicationStart"
    )) as ApplicationStartPageLabels & CommonPageLabels & InformationPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels
      }
    };
  }
}
