import { NextApiResponse } from "next";
import SubmittedContentHttpClient from "../submittedContent/SubmittedContentHttpClient";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class SubmitWebsiteContentDeliverableController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly submitContent: SubmittedContentHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const participationId = req.body?.participationId;
    const websiteContent = req.body?.websiteContent;

    await this.submitContent.saveWebsiteContentDeliverable(participationId, websiteContent);
    this.empty(res, HttpStatus.OK);
  }
}

export default SubmitWebsiteContentDeliverableController;
