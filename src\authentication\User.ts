import CreatorWithPayableStatus from "../creators/CreatorWithPayableStatus";
import UnknownCreator from "../creators/UnknownCreator";
import config from "../../config";
import CreatorWithFlaggedStatus from "../creators/CreatorWithFlaggedStatus";
import CreatorWithCreatorPrograms from "@src/creators/CreatorWithCreatorPrograms";

/**
 * @deprecated
 */
export default class User {
  public avatar: string;

  constructor(
    public id: string,
    readonly analyticsId: string | undefined,
    readonly needsMigration: boolean,
    readonly username: string,
    public status: string,
    avatar: string,
    readonly isPayable: boolean,
    readonly pointOfContactName?: string,
    public tier?: string,
    readonly isFlagged?: boolean,
    readonly programs?: string[],
    public creatorCode?: string
  ) {
    this.avatar = avatar ? `${avatar}?t=${new Date().getTime()}` : `${config.DEFAULT_AVATAR_IMAGE}`;
  }

  static signUp(exception: UnknownCreator): User {
    return new User(null, null, false, exception.userName, "UNREGISTERED", null, false);
  }

  /** @deprecated Use `forAnalyticsWithFlaggedStatus` instead once INTERESTED_CREATOR enabled in production */
  static fromCreator(creator: CreatorWithPayableStatus): User {
    return new User(
      creator.id,
      undefined,
      creator.accountInformation.needsMigration,
      creator.accountInformation.defaultGamerTag,
      creator.accountInformation.status,
      creator.avatar,
      creator.accountInformation.isPayable,
      creator.hasPointOfContact ? creator.pointOfContactName : undefined,
      creator.additionalInformation.tier
    );
  }

  static forAnalyticsWithFlaggedStatus(creator: CreatorWithFlaggedStatus): User {
    return new User(
      creator.id,
      creator.analyticsId(),
      creator.accountInformation.needsMigration,
      creator.accountInformation.defaultGamerTag,
      creator.accountInformation.status,
      creator.avatar,
      creator.accountInformation.isPayable,
      creator.hasPointOfContact ? creator.pointOfContactName : undefined,
      creator.additionalInformation?.tier,
      creator.accountInformation?.isFlagged
    );
  }

  static forAnalyticsWithCreatorPrograms(creator: CreatorWithCreatorPrograms): User {
    return new User(
      creator.id,
      creator.analyticsId(),
      creator.accountInformation.needsMigration,
      creator.accountInformation.defaultGamerTag,
      creator.accountInformation.status,
      creator.avatar,
      creator.accountInformation.isPayable,
      creator.hasPointOfContact ? creator.pointOfContactName : undefined,
      creator.additionalInformation?.tier,
      creator.accountInformation?.isFlagged,
      creator.programs,
      creator.creatorCode?.code
    );
  }

  static from(user: User): User {
    return new User(
      user.id,
      user.analyticsId,
      user.needsMigration,
      user.username,
      user.status,
      user.avatar,
      user.isPayable,
      user.pointOfContactName,
      user.tier,
      user?.isFlagged ?? null,
      user.programs ?? [],
      user.creatorCode ?? null
    );
  }

  public updateAvatar(avatar: string): void {
    this.avatar = `${avatar}?t=${new Date().getTime()}`;
  }

  isUnregistered(): boolean {
    return this.status === "UNREGISTERED";
  }

  isInactive(): boolean {
    return this.status === "INACTIVE";
  }

  isActive(): boolean {
    return this.status === "ACTIVE";
  }

  updateId(id: string): void {
    this.id = id;
  }
}
