import "reflect-metadata";
import React from "react";
import { render, screen } from "@testing-library/react";
import { mockMatchMedia } from "../../helpers/window";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { FranchisesYouPlayPageLabels } from "@src/contentManagement/FranchisesYouPlayPageMapper";
import InterestedCreatorFranchisesYouPlay from "pages/interested-creators/franchises-you-play";

jest.mock("../../../src/context/index", () => ({
  ...(jest.requireActual("../../../src/context/index") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsFranchisesYouPlay", () => {
  mockMatchMedia();
  const mockPush = jest.fn();
  const mockDispatch = jest.fn();
  const router = { locale: "en-us", push: mockPush };

  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };
  const pageLabels = {
    informationLabels: {},
    franchisesYouPlayPageLabels: {},
    commonPageLabels: {},
    breadcrumbPageLabels: {}
  } as InformationPageLabels & CommonPageLabels & BreadcrumbPageLabels & FranchisesYouPlayPageLabels;
  const interestedCreatorFranchisesYouPlayProps = {
    interestedCreator: initialInterestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    pageLabels: pageLabels,
    analytics: {} as unknown as BrowserAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator)
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: false
      }
    });
    (useDependency as jest.Mock).mockReturnValue({
      applicationsClient: {},
      errorHandler: jest.fn(),
      configuration: { BASE_PATH: "/support-a-creator" },
      metadataClient: {}
    });
  });

  it("shows remote franchises you play component", async () => {
    render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with exception code and error page", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: 500,
        sessionUser: { id: "test-user" },
        isLoading: false
      }
    });

    render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    expect(screen.queryByTestId("dynamic")).not.toBeInTheDocument();
  });

  it("shows loading state when isLoading is true", async () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {
        onboardingSteps: steps,
        exceptionCode: null,
        sessionUser: null,
        isLoading: true
      }
    });

    render(<InterestedCreatorFranchisesYouPlay {...interestedCreatorFranchisesYouPlayProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...interestedCreatorFranchisesYouPlayProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<InterestedCreatorFranchisesYouPlay {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
