import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import CreatorTypePagePropsController from "@src/serverprops/controllers/CreatorTypePagePropsController";
import config from "config";

const creatorTypeProps = (locale: string) =>
  serverPropsControllerFactory(
    new CreatorTypePagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default creatorTypeProps;
