import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import InterestedCreatorInformationPagePropsController from "./controllers/InterestedCreatorInformationPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import { interestedCreatorPages } from "pages/interested-creators/information";
import ContentManagementService from "@src/api/services/ContentManagementService";

const interestedCreatorInformationProps = (locale: string) =>
  serverPropsControllerFactory(
    new InterestedCreatorInformationPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient),
      interestedCreatorPages.information
    )
  );

export default interestedCreatorInformationProps;
