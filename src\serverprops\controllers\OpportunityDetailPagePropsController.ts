import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { FacebookPage, FacebookPages } from "@src/accounts/ConnectedAccountsHttpClient";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { OAuthError } from "@src/controllers/ConnectTikTokAccountController";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type OpportunityDetailProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
  WATERMARKS_URL: string;
  YOUTUBE_HOSTS: string[];
  TWITCH_HOSTS: string[];
  INSTAGRAM_HOSTS: string[];
  FACEBOOK_HOSTS: string[];
  TIKTOK_HOSTS: string[];
  accountConnected: boolean;
  pages: FacebookPage[];
  error: OAuthError | null;
  referer: string | null;
  invalidTikTokScope?: boolean;
  UPDATE_OPPORTUNITY_DETAILS: boolean;
  FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED: boolean;
  FLAG_CONTENT_WITH_FINAL_REMARK: boolean;
};

export default class OpportunityDetailPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<OpportunityDetailProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<OpportunityDetailProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};
    const error = this.hasSession(req, "error") ? (this.session(req, "error") as OAuthError) : null;
    const invalidTikTokScope = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : false;
    const accountConnected = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : null;

    await this.removeFromSession(req, "opportunityId");
    await this.removeFromSession(req, "INVALID_TIKTOK_SCOPE");

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "opportunities",
          "notifications",
          "add-content",
          "connect-accounts",
          "content-submission",
          "content-submission-instagram-guide",
          "content-submission-facebook-guide",
          "content-submission-youtube-guide",
          "content-submission-twitch-guide",
          "content-submission-tiktok-guide",
          "content-submission-website",
          "submit-social-media-content",
          "content-submission-upload",
          "my-content",
          "payment-information",
          "point-of-contact"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        WATERMARKS_URL: config.WATERMARKS_URL,
        YOUTUBE_HOSTS: config.YOUTUBE_HOSTS as string[],
        TWITCH_HOSTS: config.TWITCH_HOSTS as string[],
        INSTAGRAM_HOSTS: config.INSTAGRAM_HOSTS as string[],
        FACEBOOK_HOSTS: config.FACEBOOK_HOSTS as string[],
        TIKTOK_HOSTS: config.TIKTOK_HOSTS as string[],
        accountConnected,
        pages,
        error,
        referer: req.headers?.referer || null,
        invalidTikTokScope,
        UPDATE_OPPORTUNITY_DETAILS: featureFlags.isUpdateOpportunityDetailsEnabled(),
        FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED: featureFlags.isInstagramMediaSupportEnabled(),
        FLAG_CONTENT_WITH_FINAL_REMARK: featureFlags.isContentWithFinalRemarksEnabled()
      }
    };
  }
}
