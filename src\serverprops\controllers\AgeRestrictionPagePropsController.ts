import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { RequestHandlerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { AgeRestrictionPageLabels } from "@src/contentManagement/AgeRestrictionPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { GetServerSidePropsResult } from "next";
import { AgeRestrictionProps } from "pages/interested-creators/age-restriction";

export default class AgeRestrictionPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<AgeRestrictionProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<AgeRestrictionProps>> {
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "ageRestriction"
    )) as AgeRestrictionPageLabels & CommonPageLabels;
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels
      }
    };
  }
}
