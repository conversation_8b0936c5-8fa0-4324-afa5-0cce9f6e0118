import { NextApiResponse } from "next";
import SignedUrlsHttpClient, { UploadedFile } from "../submittedContent/SignedUrlsHttpClient";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { SignedURLRequestBody, SignedURLRequestBodyV2 } from "@src/api/services/SubmittedContentService";
import config from "config";

@Service()
class ViewSignedUrlController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions, private readonly signedUrls: SignedUrlsHttpClient) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const { participationId, fileName, fileId } = req.query;
    const signedURLRequestBody = req.body;
    let preSignedUrl;
    if (config.FLAG_SIGNED_URL_V2_ENABLED) {
      preSignedUrl = await this.signedUrls.preSignedUrlForV2(signedURLRequestBody as SignedURLRequestBodyV2);
    } else if (config.FLAG_SIGNED_URL_V1_ENABLED) {
      preSignedUrl = await this.signedUrls.preSignedUrlForV1(signedURLRequestBody as SignedURLRequestBody);
    } else {
      preSignedUrl = await this.signedUrls.preSignedUrlFor({ participationId, fileName, fileId } as UploadedFile);
    }
    this.json(res, preSignedUrl);
  }
}

export default ViewSignedUrlController;
