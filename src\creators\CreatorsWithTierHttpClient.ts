import CreatorWithTier from "./CreatorWithPayableStatus";
import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithTierHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithTier> {
    const response = (await this.client.get(`/v5/creators/${id}`)) as AxiosResponse;
    return Promise.resolve(CreatorWithTier.fromApi(response.data));
  }
}

export default CreatorsWithTierHttpClient;
