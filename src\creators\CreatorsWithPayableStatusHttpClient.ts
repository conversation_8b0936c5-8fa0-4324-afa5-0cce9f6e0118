import CreatorWithPayableStatus from "./CreatorWithPayableStatus";
import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithPayableStatusHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithPayableStatus> {
    const response = (await this.client.get(`/v3/creators/${id}`)) as AxiosResponse;
    return Promise.resolve(CreatorWithPayableStatus.fromApi(response.data));
  }
}

export default CreatorsWithPayableStatusHttpClient;
