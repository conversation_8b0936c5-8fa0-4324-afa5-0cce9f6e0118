import CreatorCriteria from "./CreatorCriteria";
import CreatorWithPayableStatus from "./CreatorWithPayableStatus";
import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import AvatarUpload from "../actions/Creators/Avatar/AvatarUpload";
import SaveCreatorProfileInput from "../actions/Creators/SaveCreatorProfile/SaveCreatorProfileInput";
import { UpdatedIdentity } from "./CreatorWithExpiredAccounts";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CreatorWithCreatorPrograms from "./CreatorWithCreatorPrograms";

@Service()
class CreatorsHttpClient {
  constructor(@Inject("operationsClient") protected client: TraceableHttpClient) {}

  async matching(criteria: CreatorCriteria): Promise<Array<CreatorWithPayableStatus>> {
    const response = (await this.client.get("/v2/creators", { query: criteria })) as AxiosResponse;
    return response.data
      .filter((c) => c.accountInformation.nucleusId === criteria.nucleusId)
      .map((data) => CreatorWithPayableStatus.fromApi(data));
  }

  async creatorWithPrograms(criteria: CreatorCriteria): Promise<Array<CreatorWithCreatorPrograms>> {
    const response = (await this.client.get("/v4/creators", { query: criteria })) as AxiosResponse;
    return response.data
      .filter((c) => c.accountInformation.nucleusId === criteria.nucleusId)
      .map((data) => CreatorWithCreatorPrograms.fromApi(data));
  }

  async upload(uploadedImage: AvatarUpload): Promise<void> {
    await this.client.upload(`/v1/creators/${uploadedImage.id}/avatar`, { body: uploadedImage.avatar });
  }

  async register(input: SaveCreatorProfileInput): Promise<{ id: string }> {
    const response = (await this.client.post("/v1/creators", { body: input })) as AxiosResponse;

    return response.data;
  }

  async update(input: SaveCreatorProfileInput): Promise<void> {
    await this.client.put(`/v1/creators/${input.id}`, { body: input });
  }

  async syncIdentity(identity: UpdatedIdentity): Promise<void> {
    await this.client.put(`/v1/creators/${identity.id}`, { body: identity });
  }
}

export default CreatorsHttpClient;
