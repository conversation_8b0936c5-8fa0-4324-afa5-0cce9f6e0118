import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ClearAccountTypeController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.removeFromSession(req, "accountType");
    this.empty(res, HttpStatus.OK);
  }
}

export default ClearAccountTypeController;
