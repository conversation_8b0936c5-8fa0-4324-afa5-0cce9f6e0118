import "reflect-metadata";
import React, { ComponentType, memo, useCallback, useState } from "react";
import Error from "../_error";
import { useAppContext } from "@src/context";
import { useToast } from "../../components/toast";
import MigrationLayout from "../../components/MigrationLayout";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import config from "../../config";
import { interestedCreatorPages } from "./information";
import { useRouter } from "next/router";
import BrowserAnalytics, {
  AuthenticatedUser,
  AuthenticatedUserFactory,
  InitialInterestedCreator
} from "../../src/analytics/BrowserAnalytics";
import flags from "../../utils/feature-flags";
import { GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import interestedCreatorCreatorTypesProps from "@src/serverprops/InterestedCreatorCreatorTypesProps";
import verifyRequestToJoin from "@src/serverprops/middleware/VerifyRequestToJoin";
import dynamic from "next/dynamic";
import Loading from "@components/Loading";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { CreatorTypePageLabels } from "@src/contentManagement/CreatorTypePageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { useDependency } from "@src/context/DependencyContext";

const CreatorType: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/CreatorType"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type CreatorType = {
  imageAsIcon: string;
  value: string;
  label: string;
};

const CREATORS_TYPE: string[] = [
  "YOUTUBER",
  "LIFESTYLE",
  "PHOTOGRAPHER",
  "DESIGNER_ARTIST",
  "BLOGGER",
  "LIVE_STREAMER",
  "PODCASTER",
  "COSPLAYER",
  "ANIMATOR",
  "SCREENSHOTER",
  "OTHER"
];

export type InterestedCreatorsCreatorType = {
  nucleusId?: number;
  creatorTypes?: CreatorType[] | string[];
};

export type InterestedCreatorCreatorTypeProps = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InterestedCreator;
  analytics?: BrowserAnalytics;
  user: AuthenticatedUser;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  pageLabels: InformationPageLabels & BreadcrumbPageLabels & CreatorTypePageLabels & CommonPageLabels;
};

export default memo(function InterestedCreatorCreatorType({
  interestedCreator,
  user,
  analytics = new BrowserAnalytics(user),
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  pageLabels
}: InterestedCreatorCreatorTypeProps) {
  const { breadcrumbPageLabels, creatorTypePageLabels, commonPageLabels } = pageLabels;
  const [showConfirmation, setShowConfirmation] = useState(false);
  const {
    dispatch,
    state,
    state: { exceptionCode = null, sessionUser = null, isLoading = false } = {}
  } = useAppContext();
  const { applicationsClient, metadataClient, configuration: config, errorHandler } = useDependency();
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const router = useRouter();

  const onClose = useCallback(() => {
    setShowConfirmation(true);
  }, []);
  const onGoBack = () => router.push("/interested-creators/information");
  const logout = useCallback(() => {
    analytics.cancelledCreatorApplication({ locale: router.locale, page: location.pathname });
    router.push("/api/logout");
  }, [router]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={creatorTypePageLabels.creatorTypePageTitle}
      className="interested-creator"
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        franchisesYouPlay: commonPageLabels.franchises,
        information: commonPageLabels.information,
        creatorType: commonPageLabels.creatorType
      }}
      onClose={onClose}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      onGoBack={onGoBack}
      isLoading={isLoading}
    >
      <CreatorType
        labels={{
          ...commonPageLabels,
          ...creatorTypePageLabels,
          ...breadcrumbPageLabels,
          requiredMessage: (creatorTypePageLabels.messages as unknown as { creatorTypes: string }).creatorTypes,
          creatorsTypeLabels: CREATORS_TYPE.map((creatorType) => ({
            value: creatorType,
            label: creatorTypePageLabels.labels[creatorType.toLocaleLowerCase()]
          }))
        }}
        redirectedToNextStepUrl="/interested-creators/franchises-you-play"
        interestedCreator={interestedCreator}
        analytics={analytics}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        stableDispatch={stableDispatch}
        state={state}
        errorHandling={errorHandler}
        onClose={onClose}
        setShowConfirmation={setShowConfirmation}
        showConfirmation={showConfirmation}
        configuration={{ metadataClient: metadataClient, applicationsClient: applicationsClient }}
        router={router}
        locale={router.locale}
        errorToast={errorToast}
        handleCancelRegistration={logout}
        basePath={config.BASE_PATH}
      />
    </MigrationLayout>
  );
});

export const getServerSideProps = async ({ req, res, locale }: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(verifyRequestToJoin)
      .get(interestedCreatorCreatorTypesProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<InterestedCreatorCreatorTypeProps>;
  }
  const interestedCreator = await withInterestedCreator(req, res, interestedCreatorPages.creatorTypes);
  if (!config.INTERESTED_CREATOR || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "creatorType");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
      pageLabels,
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled()
    }
  };
};
