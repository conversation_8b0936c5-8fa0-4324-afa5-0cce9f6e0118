import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import InterestedCreator from "../interestedCreators/InterestedCreator";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { Information } from "pages/interested-creators/information";

@Service()
export default class UpdateApplicationController extends AuthenticatedRequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const identity = this.identity(req);
    const nucleusId = identity.nucleusId;
    const defaultInterestedCreator = {
      originEmail: identity.email,
      nucleusId,
      defaultGamerTag: identity.username,
      dateOfBirth: identity.dateOfBirth.toString()
    } as InterestedCreator;
    const interestedCreatorFromSession = this.hasSession(req, `${identity.programs[0].code}.interestedCreator`)
      ? typeof this.session(req, `${identity.programs[0].code}.interestedCreator`) === "boolean"
        ? this.session(req, `${identity.programs[0].code}.interestedCreator`) === "true"
          ? (this.session(req, `${identity.programs[0].code}.interestedCreator`) as InterestedCreator & Information)
          : defaultInterestedCreator
        : (this.session(req, `${identity.programs[0].code}.interestedCreator`) as InterestedCreator & Information)
      : null;

    const interestedCreator = !interestedCreatorFromSession
      ? new InterestedCreator()
      : new InterestedCreator(interestedCreatorFromSession);
    const updatedCreator = { ...interestedCreator, ...req.body };

    await this.addToSession(req, `${identity.programs[0].code}.interestedCreator`, updatedCreator);

    this.json(res, updatedCreator);
  }
}
