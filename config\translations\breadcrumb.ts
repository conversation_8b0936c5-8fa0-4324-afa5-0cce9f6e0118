export default function labelsBreadCrumb(t) {
  return {
    migration: {
      information: t("common:information"),
      franchisesYouPlay: t("breadcrumb:franchisesYouPlay"),
      creatorType: t("common:creatorType"),
      connectAccounts: t("breadcrumb:connectAccounts"),
      communicationPreferences: t("breadcrumb:communicationPreferences"),
      termsAndConditions: t("breadcrumb:termsAndConditions")
    },
    modal: {
      modalTitle: t("breadcrumb:modalTitle"),
      modalMessage: t("breadcrumb:modalMessage")
    }
  };
}
