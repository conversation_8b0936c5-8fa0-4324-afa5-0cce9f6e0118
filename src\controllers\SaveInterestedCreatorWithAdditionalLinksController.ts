import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import InterestedCreatorsHttpClient from "../interestedCreators/InterestedCreatorsHttpClient";
import InterestedCreator from "../interestedCreators/InterestedCreator";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class SaveInterestedCreatorWithAdditionalLinksController extends Request<PERSON>and<PERSON> implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly interestedCreators: InterestedCreatorsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.interestedCreators.save(req.body as InterestedCreator);
    this.empty(res);
  }
}

export default SaveInterestedCreatorWithAdditionalLinksController;
