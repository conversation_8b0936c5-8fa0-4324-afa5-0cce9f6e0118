import "reflect-metadata";
import config from "../../config";
import CachedTermsAndConditions from "../pactSafe/CachedTermsAndConditions";
import ApiContainer from "../ApiContainer";
import { Redirect } from "../middleware/types";
import withLocalizedUrl from "./WithLocalizedUrl";
import RedirectException from "./RedirectException";
import { addTelemetryInformation, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import User from "@src/authentication/User";

/**
 * @deprecated
 */
export default async function withTermsAndConditionsUpToDate(
  req: NextApiRequestWithSession,
  locale: string,
  user: User
): Promise<void> {
  await addTelemetryInformation(req, null, () => Promise.resolve());
  const redirect: Redirect = {
    redirect: {
      destination: encodeURI(`${withLocalizedUrl(req, locale)}terms-and-conditions`),
      statusCode: 302
    }
  };

  if (User.from(user).isUnregistered()) {
    throw new RedirectException(redirect);
  }

  const termsAndConditions: CachedTermsAndConditions = ApiContainer.get(CachedTermsAndConditions);
  const isStatusUpToDate = await termsAndConditions.signedStatusWithProgram(user.id, locale, config.PROGRAM_CODE);

  if (!isStatusUpToDate.upToDate) {
    throw new RedirectException(redirect);
  }

  return Promise.resolve();
}
