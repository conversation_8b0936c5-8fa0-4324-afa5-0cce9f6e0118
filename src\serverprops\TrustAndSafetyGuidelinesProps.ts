import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import TrustAndSafetyGuidelinesPagePropsController from "./controllers/TrustAndSafetyGuidelinesPagePropsController";
import config from "config";

const trustAndSafetyGuidelinesProps = (locale: string) =>
  serverPropsControllerFactory(
    new TrustAndSafetyGuidelinesPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default trustAndSafetyGuidelinesProps;
