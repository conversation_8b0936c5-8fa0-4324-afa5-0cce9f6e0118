import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { FacebookPage, FacebookPages } from "@src/accounts/ConnectedAccountsHttpClient";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { OAuthError } from "@src/controllers/ConnectTikTokAccountController";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export type ConnectAccountsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user?: AuthenticatedUser;
  pages: FacebookPage[];
  invalidTikTokScope: boolean;
  error: OAuthError | null;
};

export default class ConnectAccountsPagePropController
  extends AuthenticatedRequestH<PERSON><PERSON>
  implements ServerPropsController<ConnectAccountsProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<ConnectAccountsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};
    const error = this.hasSession(req, "error") ? (this.session(req, "error") as OAuthError) : null;
    const invalidTikTokScope = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : false;

    await this.removeFromSession(req, "error");
    await this.removeFromSession(req, "INVALID_TIKTOK_SCOPE");

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        pages,
        error,
        invalidTikTokScope,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "breadcrumb",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
