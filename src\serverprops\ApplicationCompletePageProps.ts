import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationCompletePagePropsController from "./controllers/ApplicationCompletePagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import ContentManagementService from "@src/api/services/ContentManagementService";

const applicationCompletePageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationCompletePagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient),
      null
    )
  );

export default applicationCompletePageProps;
