.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper,
.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper {
  @apply bg-[transparent];
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon,
.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon {
  @apply h-meas12 w-meas12;
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper {
  @apply w-auto;
}

.topnav-child-container > .notifications-bell-container > .notification-dropdown-container {
  @apply mr-meas0;
}

.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container {
  @apply ml-meas7 mr-meas0 w-auto;
}

.topnav-mobile-child-container > .notificaion-bell-parent-container {
  @apply flex items-center;
}

.topnav-mobile-child-container > .notificaion-bell-parent-container > .notifications-bell-container {
  @apply ml-meas0;
}

.notification-bell-text {
  @apply text-left font-text-bold text-white xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.navigation-button-image {
  @apply ml-meas0 h-meas20 w-meas20 cursor-pointer rounded-[40px];
}

.navigation-button-image:hover {
  @apply border-2 border-white;
}

.profile-avatar-container {
  @apply pb-meas4;
}
