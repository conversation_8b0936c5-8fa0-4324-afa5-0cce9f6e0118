export default function labelsConnectAccounts(t) {
  return {
    title: t("connect-accounts:title"),
    message: t("connect-accounts:message"),
    subTitle: t("connect-accounts:subTitle"),
    myAccount: t("connect-accounts:myAccount"),
    addAccount: t("connect-accounts:addAccount"),
    description: t("connect-accounts:description"),
    accounts: {
      youTube: t("connect-accounts:accounts:youTube"),
      facebook: t("connect-accounts:accounts:facebook"),
      twitch: t("connect-accounts:accounts:twitch"),
      instagram: t("connect-accounts:accounts:instagram"),
      tiktok: t("connect-accounts:accounts:tiktok")
    },
    modalTitle: t("connect-accounts:modalTitle"),
    modalMessage: t("connect-accounts:modalMessage"),
    modalConfirmationTitle: t("common:modalConfirmationTitle"),
    modalConfirmationTitleFB: t("connect-accounts:modalConfirmationTitleFB"),
    confirmationDesc1: t("common:confirmationDesc1"),
    confirmationDesc2: t("common:confirmationDesc2"),
    messages: {
      removeAccountTitle: t("connect-accounts:messages:removeAccountTitle"),
      removeAccountDescription: t("connect-accounts:messages:removeAccountDescription"),
      cannotConnectInstaAccount: t("connect-accounts:messages:cannotConnectInstaAccount"),
      cannotConnectInstaAccountHeader: t("connect-accounts:messages:cannotConnectInstaAccountHeader"),
      actionTitle: t("connect-accounts:messages:actionTitle"),
      actionDescription1: t("connect-accounts:messages:actionDescription1"),
      actionDescription2: t("connect-accounts:messages:actionDescription2"),
      actionDescription3: t("connect-accounts:messages:actionDescription3"),
      actionDescription4: t("connect-accounts:messages:actionDescription4"),
      youtubeNoChannelError: t("connect-accounts:messages:youtubeNoChannelError")
    },
    modal: {
      removeAccountTitle: t("connect-accounts:removeAccountTitle"),
      removeAccountDescription1: t("connect-accounts:removeAccountDescription1"),
      removeAccountDescription2: t("connect-accounts:removeAccountDescription2")
    },
    buttons: {
      cancel: t("connect-accounts:cancel"),
      remove: t("connect-accounts:remove")
    },
    subscribers: t("connect-accounts:subscribers"),
    removeAccount: t("connect-accounts:removeAccount"),
    expireAccount: t("connect-accounts:expireAccount"),
    or: t("connect-accounts:or"),
    reconnectAccount: t("connect-accounts:reconnectAccount"),
    connectNewAccount: t("connect-accounts:connectNewAccount"),
    connectNewAccountDescription: t("connect-accounts:connectNewAccountDescription"),
    connectNewAccountDescriptionWithTikTok: t("connect-accounts:connectNewAccountDescriptionWithTikTok"),
    comma: t("connect-accounts:comma"),
    verificationPending: t("connect-accounts:verificationPending"),
    reVerifyAccount: t("connect-accounts:reVerifyAccount")
  };
}
