.mg-form-container {
  @apply mt-meas16 flex w-full flex-col justify-between border-t border-white border-opacity-[0.33] pt-meas16 md:w-[640px];
}
.mg-form-container .input-box-label,
.mg-form-container .select-label {
  @apply text-gray-10;
} /* Adding parent class for .select-label as we should not override core-ui-kit select label without parent class. It will impact globally*/
.mg-form-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.information-form {
  @apply grid grid-cols-1 gap-y-meas10 md:grid-cols-2 md:gap-x-meas20 md:gap-y-meas12;
}
.information {
  @apply flex flex-1 flex-col;
}

.information-title {
  @apply font-display-bold font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.information-subtitle {
  @apply pt-[10px] font-text-regular xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.information-avatar {
  @apply mb-meas16 mt-[34px] flex justify-center md:mb-meas0 md:mt-meas0 md:justify-end md:self-end;
}
.mg-platform-container {
  @apply my-meas16 flex w-full flex-col items-center justify-center border-b border-t border-white border-opacity-[0.33] py-meas16 md:w-[640px];
}
.mg-platform-title {
  @apply pb-meas10 font-display-regular font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.mg-platform-description {
  @apply font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-platform-container .empty-card {
  @apply mt-meas21;
}
.mg-platform-container .select-box {
  @apply mb-[12.97px] h-meas20 w-[290px] w-full md:mb-[13.55px] md:w-[320px];
}
.mg-platform-container .select-box .select-header {
  @apply h-meas20;
}
.mg-platform-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.mg-platform-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.mg-sc-platform-container {
  @apply flex w-full flex-col items-center justify-center overflow-hidden xl:w-[1070px];
}
.mg-sc-platform-container .mg-intro {
  @apply mb-[60px];
}
.mg-secondary-title {
  @apply pb-meas10 font-display-regular font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.mg-secondary-title[data-disabled="true"] {
  @apply opacity-50;
}
.mg-form-container .input-box,
.select-header-label,
.from-text-field {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-form-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.information-field {
  @apply flex min-h-[2.5rem] items-center font-text-regular text-gray-10 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-information-additional-content-container {
  @apply mb-meas16 mt-meas16 flex w-full flex-col justify-between border-t border-white border-opacity-[0.33] pt-meas16 md:w-[672px];
}
.mg-information-additional-content {
  @apply flex w-full flex-col gap-meas10;
}
.mg-information-additional-content .content-in-center {
  @apply flex w-[91.666667%] flex-col items-center md:w-[640px] xl:w-[790px];
}
.mg-information-additional-content-title {
  @apply font-display-bold text-[1.5rem] leading-8 tracking-[1px] text-white xs:text-mobile-h4 md:text-center md:text-tablet-h4 lg:text-desktop-h4;
}
.mg-information-additional-content-description {
  @apply text-center font-text-regular text-[1rem] font-normal leading-6;
}
