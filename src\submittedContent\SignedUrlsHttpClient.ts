import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import { v4 as uuid } from "uuid";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { SignedURLRequestBody, SignedURLRequestBodyV2 } from "@src/api/services/SubmittedContentService";
import config from "config";

export type PreSignedUrl = {
  url: string;
  fileId: string;
};

export type PreSignedUrlV2 = {
  id: string;
  url: string;
};

export type UploadedFile = {
  participationId: string;
  fileName: string;
  fileId?: string | null;
};

export const signedUrlFlag = () => {
  if (config.FLAG_SIGNED_URL_V2_ENABLED) {
    return "contentScanningClient";
  }
  return config.FLAG_SIGNED_URL_V1_ENABLED ? "contentScanningClient" : "contentSubmissionClient";
};

@Service()
class SignedUrlsHttpClient {
  constructor(@Inject(signedUrlFlag()) private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/viewContentUploadPreSignedUrlWithListing View pre-signed URL for uploaded file (V2)}
   */
  async preSignedUrlForV2(signedURLRequestBodyV2: SignedURLRequestBodyV2): Promise<PreSignedUrlV2> {
    const response = (await this.client.post(`/v2/upload-signed-urls`, {
      body: signedURLRequestBodyV2
    })) as AxiosResponse<PreSignedUrlV2>;
    return Promise.resolve({ ...response.data });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/viewContentUploadPreSignedUrl View pre-signed URL for uploaded file}
   */
  async preSignedUrlForV1(signedURLRequestBody: SignedURLRequestBody): Promise<PreSignedUrl> {
    const response = (await this.client.post(`/v1/upload-signed-urls`, {
      body: signedURLRequestBody
    })) as AxiosResponse<PreSignedUrl>;
    return Promise.resolve({ ...response.data });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#operation/uploadContentConfiguration View pre-signed URL for uploaded file}
   */
  async preSignedUrlFor(uploadedFile: UploadedFile): Promise<PreSignedUrl> {
    const fileId = uploadedFile.fileId ? uploadedFile.fileId : uuid();
    const response = (await this.client.get(`/v1/participation/${uploadedFile.participationId}/signed-url`, {
      query: { uuid: fileId, fileName: uploadedFile.fileName }
    })) as AxiosResponse<PreSignedUrl>;
    return Promise.resolve({ fileId, ...response.data });
  }
}

export default SignedUrlsHttpClient;
