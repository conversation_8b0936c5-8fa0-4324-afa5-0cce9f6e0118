import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import { SendOpportunityPOCEmail } from "../actions/Creators/SendOpportunityPOCEmail/SendOpportunityPOCEmail";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";
import EmailsHttpClient from "@src/opportunities/EmailsHttpClient";

@Service()
class SendEmailToOpportunityPocController extends Request<PERSON>andler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions, private readonly opportunities: EmailsHttpClient) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const email: SendOpportunityPOCEmail = req.body;
    await this.opportunities.sendEmailToOpportunityPOC(email);
    this.empty(res, HttpStatus.OK);
  }
}

export default SendEmailToOpportunityPocController;
