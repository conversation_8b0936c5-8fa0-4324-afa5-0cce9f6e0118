import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { InterestedCreatorFranchisesYouPlayProps } from "../../../pages/interested-creators/franchises-you-play";
import featureFlags from "utils/feature-flags";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import { interestedCreatorPages } from "pages/interested-creators/information";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { FranchisesYouPlayPageLabels } from "@src/contentManagement/FranchisesYouPlayPageMapper";
import { BreadcrumbPageLabels } from "@src/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import crypto from "crypto";

export default class InterestedCreatorFranchisesYouPlayPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<InterestedCreatorFranchisesYouPlayProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly applications: InterestedCreatorApplicationsHttpClient,
    private readonly page?: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession
  ): Promise<GetServerSidePropsResult<InterestedCreatorFranchisesYouPlayProps>> {
    const identity = this.identity(req);
    const nucleusId = identity.nucleusId;
    const program = identity.programs[0].code;

    const defaultInterestedCreator = {
      originEmail: identity.email,
      nucleusId,
      defaultGamerTag: identity.username,
      dateOfBirth: identity.dateOfBirth,
      analyticsId: crypto.createHash("sha256").update(nucleusId.toString()).digest("base64"),
      creatorTypes: identity.creatorTypes || []
    };

    const interestedCreatorFromSession = this.hasSession(req, `${program}.interestedCreator`)
      ? this.session(req, `${program}.interestedCreator`) === true
        ? defaultInterestedCreator
        : (this.session(req, `${program}.interestedCreator`) as InterestedCreator)
      : null;

    const interestedCreator = await this.getInterestedCreator(nucleusId, interestedCreatorFromSession);
    if (!featureFlags.isInterestedCreatorFlowEnabled() || !interestedCreator) return { notFound: true };
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "franchisesYouPlay"
    )) as InformationPageLabels & FranchisesYouPlayPageLabels & CommonPageLabels & BreadcrumbPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        interestedCreator,
        user: AuthenticatedUserFactory.fromInterestedCreator(interestedCreator as InitialInterestedCreator),
        INTERESTED_CREATOR_REAPPLY_PERIOD: featureFlags.isInterestedCreatorReApplyEnabled(),
        pageLabels
      }
    };
  }

  private async getInterestedCreator(
    nucleusId: number,
    interestedCreator: InterestedCreator
  ): Promise<InterestedCreator | null> {
    if (!config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      return interestedCreator;
    }

    let existingApplicantInformation;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplicantInformation = await this.applications.forCreatorWithProgram(nucleusId, config.PROGRAM_CODE);
    } else {
      existingApplicantInformation = await this.applications.forCreatorWithApplicationStatus(nucleusId);
    }
    if (!existingApplicantInformation) {
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.information) {
      interestedCreator = { ...existingApplicantInformation.applicantInformation, ...interestedCreator };
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.creatorTypes) {
      if (
        (interestedCreator.creatorTypes?.length == 0 || interestedCreator.creatorTypes === undefined) &&
        existingApplicantInformation
      ) {
        interestedCreator.creatorTypes = existingApplicantInformation.applicantInformation?.creatorTypes;
      }
      return interestedCreator;
    }

    if (this.page === interestedCreatorPages.franchises) {
      interestedCreator = {
        ...interestedCreator,
        preferredFranchises: [
          ...interestedCreator.preferredFranchises,
          ...(existingApplicantInformation.applicantInformation?.preferredFranchises ?? [])
        ]
      };
      return interestedCreator;
    }

    interestedCreator = {
      ...interestedCreator,
      createdDate: existingApplicantInformation.createdDateformattedWithoutTime(this.currentLocale)
    };

    return interestedCreator;
  }
}
