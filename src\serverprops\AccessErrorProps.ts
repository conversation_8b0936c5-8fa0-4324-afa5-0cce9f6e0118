import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import AccessErrorPagePropsController from "@src/serverprops/controllers/AccessErrorPagePropsController";

const accessErrorProps = (locale: string) =>
  serverPropsControllerFactory(new AccessErrorPagePropsController(ApiContainer.get("options"), locale));

export default accessErrorProps;
