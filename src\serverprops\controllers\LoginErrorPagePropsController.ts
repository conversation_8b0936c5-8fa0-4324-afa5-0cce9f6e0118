import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type LoginErrorProps = {
  runtimeConfiguration?: Record<string, unknown>;
  futureCreator: boolean;
};

export default class LoginErrorPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<LoginErrorProps>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<LoginErrorProps>> {
    if (featureFlags.isInterestedCreatorFlowEnabled()) {
      return { notFound: true };
    }

    const futureCreator = this.hasSession(req, "notify") ? (this.session(req, "notify") as boolean) : false;
    if (!futureCreator) {
      return { notFound: true };
    }

    await this.removeFromSession(req, "notify");

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        futureCreator,
        ...(await serverSideTranslations(this.currentLocale, ["common", "login-error"]))
      }
    };
  }
}
