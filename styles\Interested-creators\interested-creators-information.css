.information.interested-creator-information {
  @apply mb-meas16 md:self-center;
}

.interested-creator-information-language {
  @apply border-t border-gray-10 border-opacity-[0.33];
}

.interested-creator-information-container {
  @apply w-[91.666667%] pb-meas26 md:w-[640px] xl:w-[790px];
}

.interested-creator .content-languages,
.interested-creator .content-in-center {
  @apply flex w-[91.666667%] flex-col md:w-[640px] md:items-center xl:w-[790px];
}

.interested-creator .content-in-center.language {
  @apply border-0 p-meas0;
}

.interested-creator .content-in-center.language .select-box {
  @apply w-full !important;
}

.interested-creator .content-in-center.social-media {
  @apply border-t-0 pt-meas0;
}

.interested-creator .content-language.mg-communication-title {
  @apply font-display-bold;
}
.interested-creator .mg-communication-description {
  @apply pt-meas13 md:pt-meas6;
}
.interested-creator .content-language .select-label,
.interested-creator .language .select-label {
  @apply self-start font-display-bold;
}
.interested-creator-information-content-media-container {
  @apply mt-meas32 flex w-full flex-col gap-meas8 border-t border-gray-10 border-opacity-[0.33] py-meas16;
}

.interested-creator-information-content-media-container > .interested-creator-information-content-media-title {
  @apply text-left font-display-bold text-gray-10 xs:text-mobile-h4 md:text-center md:text-tablet-h4 lg:text-desktop-h4;
}

.interested-creator-information-content-media-container > p {
  @apply my-meas8 font-display-regular text-gray-10 xs:text-mobile-body-default md:text-center md:text-tablet-body-default lg:text-desktop-body-default;
}

.interested-creator-information-content-media {
  @apply w-full;
}

.interested-creator-information-content-media > li > label:first-child {
  @apply col-span-4 md:col-span-3;
}

.interested-creator-information-content-media li > label:last-of-type {
  @apply col-span-2 w-[154px] md:col-span-1;
}

.interested-creator-information-content-media > li {
  @apply mb-meas7 grid grid-cols-5 gap-x-meas20 gap-y-meas12;
  grid-template-rows: auto;
}

.interested-creator-information-content-media > li > label:nth-child(2) {
  @apply whitespace-nowrap;
}

.interested-creator-content-media-delete {
  @apply col-span-2 mt-[14px] place-self-center md:col-span-1 md:my-auto;
}

.interested-creator .mg-header-logo a > span {
  @apply font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}

.interested-creator form .multi-select-button,
.interested-creator form .input-text-field::placeholder {
  color: #a6a6a6;
}

.interested-creator form .multi-select-button {
  @apply justify-center;
}

.interested-creator form .input-box-label {
  @apply font-display-bold;
}

.interested-creator-content-url-add-more {
  @apply mb-meas18 mt-meas13 flex w-fit cursor-pointer md:mb-[74px] xl:mb-[60px] xl:mt-meas16;
}
.interested-creator-content-url-add-more-icon,
.mg-content-url-add-more-icon {
  @apply h-meas10 w-meas10 text-gray-10;
}
.interested-creator-content-url-add-more-text,
.mg-content-url-add-more-text {
  @apply ml-[6px] self-center font-display-bold leading-4 tracking-[1px] text-gray-10 xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.interested-creator-content-url-icon,
.mg-content-url-icon {
  @apply h-[18px] text-gray-10;
}

.interested-creator .intrested-creator-information-description {
  @apply flex justify-center text-center font-text-regular xs:text-mobile-body-large md:w-[80%] md:text-tablet-body-large lg:text-desktop-body-large;
}

.intrested-creator-information-sub-description {
  @apply w-[91.666667%] xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}

.intrested-creator-information-description .gamer-tag {
  @apply font-text-bold;
}

.intrested-creator-information-description p {
  @apply max-w-[790px] pl-meas4 pr-meas4 md:pl-meas0 md:pr-meas0;
}

.add-account-container {
  @apply mt-meas26 pb-meas16;
}

.add-account-description {
  @apply flex flex-row pb-meas0 pt-meas10 font-text-regular font-normal leading-6 xs:text-mobile-body-default md:text-center md:text-tablet-body-default lg:text-desktop-body-default;
}

.interested-creator .connect-accounts {
  @apply xl:w-[inherit];
}

.interested-creator .connect-accounts-form,
.interested-creator .connect-accounts,
.interested-creator .mg-connected-accounts-container {
  @apply xl:w-[inherit];
}

.interested-creator .mg-connected-accounts-container {
  @apply mb-meas0 mt-meas0 border-0 pt-meas10;
}

.interested-creator .mg-connected-accounts-title {
  @apply pb-meas10;
}

.interested-creator .account-card-channel-name {
  @apply pb-meas0;
}

/**This will be deleted once we integrate webite Or additional links section*/
.add-account-container ~ .social-media > .interested-creator-information-content-media-container {
  @apply mt-meas0;
}

.interested-creator-information-additional-content-container {
  @apply flex w-full flex-col gap-meas10 pb-meas16 md:pb-meas38;
}

.interested-creator-information-additional-content-title {
  @apply text-left font-display-bold leading-7 tracking-[1px] text-gray-10 xs:text-mobile-h4 md:text-center md:text-tablet-h4 lg:text-desktop-h4;
}

.interested-creator-information-additional-content-urls,
.mg-information-additional-content-urls {
  @apply flex w-full flex-col self-start;
}

.interested-creator-information-additional-content-url,
.mg-information-additional-content-url {
  @apply mb-meas0 flex w-full;
}

.interested-creator-information-additional-content-url:nth-child(n + 2),
.mg-information-additional-content-url:nth-child(n + 2) {
  @apply mt-meas10;
}

/** Added this compound selector in order to override input component label */
.interested-creator-information-additional-content-url > label:last-of-type,
.mg-information-additional-content-url > label:last-of-type {
  @apply col-span-2 w-full flex-[1] md:flex-[0.9];
}

.interested-creator-information-additional-content-url-without-delete > label:last-of-type,
.mg-information-additional-content-url-without-delete > label:last-of-type {
  @apply md:flex-1;
}

.interested-creator-information-additional-content-add-more,
.mg-information-additional-content-add-more {
  @apply flex w-fit cursor-pointer self-start;
}

.interested-creator-information-additional-content-delete,
.mg-information-additional-content-delete {
  @apply mt-meas10 flex h-meas20 flex-[0.2] items-center justify-center md:flex-[0.1];
}

.interested-creator-information-additional-content-description {
  @apply font-text-regular text-[1rem] font-normal leading-6 md:text-center;
}

/** Added the below css for overriding the ConnectAccountsForm component used in My Profile(My accounts & Add accounts sections are there)*/
.add-account-container .mg-connected-accounts-title {
  @apply font-bold;
}

.add-account-container .content-in-center {
  @apply w-full;
}

.add-account-container .mg-connected-accounts-container {
  @apply w-auto;
}

.add-account-container .connect-account-card-container {
  @apply gap-[30px];
}

.add-account-card .connected-acc-card-container {
  @apply grid-cols-1 self-center md:grid-cols-1 xl:grid-cols-3 xl:self-start;
}

.interested-creator .multi-select-menu {
  @apply transition-all duration-300 ease-in-out;
}

.interested-creator .multi-select-menu:has(.multi-select-items) {
  @apply md:mb-meas10;
}
