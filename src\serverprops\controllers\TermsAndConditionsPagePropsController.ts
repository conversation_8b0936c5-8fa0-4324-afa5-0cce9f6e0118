import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { TermsAndConditionsProps } from "../TermsAndConditionsProps";
import featureFlags from "utils/feature-flags";

export default class TermsAndConditionsPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<TermsAndConditionsProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<TermsAndConditionsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const opportunityId = this.hasSession(req, "opportunityId") ? (this.session(req, "opportunityId") as string) : null;
    const initialPage = this.hasSession(req, `${this.program}.initialPage`)
      ? (this.session(req, `${this.program}.initialPage`) as string)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        opportunityId,
        initialPage,
        user: authenticatedUser,
        locale: this.currentLocale,
        urlLocale: this.localePathSegment(req, this.currentLocale),
        FLAG_COUNTRIES_BY_TYPE: featureFlags.isCountriesByTypeEnabled(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "breadcrumb",
          "information",
          "terms-and-conditions",
          "opportunities"
        ]))
      }
    };
  }
}
