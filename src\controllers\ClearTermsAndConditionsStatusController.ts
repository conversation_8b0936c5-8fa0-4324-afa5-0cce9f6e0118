import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import CachedTermsAndConditions from "../pactSafe/CachedTermsAndConditions";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import config from "config";

@Service()
class ClearTermsAndConditionsStatusController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly termsAndConditions: CachedTermsAndConditions
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const identity = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    const { locale } = req.query;

    await this.termsAndConditions.clearSignedStatusForProgram(identity.id, locale as string, config.PROGRAM_CODE);

    this.json(res, {});
  }
}

export default ClearTermsAndConditionsStatusController;
