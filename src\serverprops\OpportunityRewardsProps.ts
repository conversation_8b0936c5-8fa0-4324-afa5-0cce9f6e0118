import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import OpportunityRewardsPagePropsController from "./controllers/OpportunityRewardsPagePropsController";
import config from "config";

const opportunityRewardsProps = (locale: string) =>
  serverPropsControllerFactory(
    new OpportunityRewardsPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default opportunityRewardsProps;
