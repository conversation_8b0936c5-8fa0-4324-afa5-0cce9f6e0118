export default function labelsFranchisesYouPlay(t) {
  return {
    title: t("franchises-you-play:title"),
    primaryFranchiseTitle: t("franchises-you-play:primaryFranchiseTitle"),
    primaryFranchiseSubTitle: t("franchises-you-play:primaryFranchiseSubTitle"),
    secondaryFranchiseTitle: t("franchises-you-play:secondaryFranchiseTitle"),
    secondaryFranchiseSubTitle: t("franchises-you-play:secondaryFranchiseSubTitle"),
    description: t("franchises-you-play:description"),
    modalConfirmationTitle: t("common:modalConfirmationTitle"),
    confirmationDesc1: t("common:confirmationDesc1"),
    confirmationDesc2: t("common:confirmationDesc2"),
    messages: {
      primaryFranchise: t("franchises-you-play:messages.primaryFranchise"),
      success: {
        header: t("franchises-you-play:success:updatedInformationHeader"),
        content: t("franchises-you-play:success:franchiseUpdate")
      }
    },
    labels: {
      primaryFranchise: t("franchises-you-play:labels.primaryFranchise"),
      loadMore: t("franchises-you-play:labels.loadMore")
    }
  };
}
