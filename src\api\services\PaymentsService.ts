import { AxiosResponse } from "axios";
import client from "./Client";
import { PaymentsIFrameUrlPayload } from "@components/pages/payment-information/usePayableStatus";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

type AmountUnit = "B" | "M" | "K" | "";

type CurrencySymbol = "$";

type Amount = {
  integralPart: string;
  decimalPart: string;
};

export class FormattedAmount {
  readonly amount: Amount;

  constructor(integralPart: string, decimalPart: string, readonly unit: AmountUnit, readonly currency: CurrencySymbol) {
    this.amount = { integralPart, decimalPart };
  }

  toString(): string {
    return `${this.currency}${this.amount.integralPart}.${this.amount.decimalPart}${this.unit}`;
  }
}
export class DollarAmount {
  private readonly ONE_BILLION = 1.0e9;
  private readonly ONE_MILLION = 1.0e6;
  private readonly ONE_THOUSAND = 1.0e3;
  private readonly units: AmountUnit[] = ["K", "M", "B"];
  private readonly divisors = { K: this.ONE_THOUSAND, M: this.ONE_MILLION, B: this.ONE_BILLION };
  private readonly currency: CurrencySymbol = "$";

  constructor(private readonly amount: string) {}

  abbreviateOn(unit: AmountUnit): FormattedAmount {
    const units = this.units.slice(this.units.indexOf(unit));
    const amount = Math.abs(Number(this.amount));

    let amountUnit: AmountUnit = "";
    const divisor = units.reverse().find((aUnit) => {
      const isDivisor = amount >= this.divisors[aUnit];
      if (isDivisor) amountUnit = aUnit;
      return isDivisor;
    });

    const formattedAmount = !divisor ? this.formatNumber(amount) : this.formatNumber(amount / this.divisors[divisor]);
    const [integralPart, decimalPart] = formattedAmount.split(".");

    return new FormattedAmount(integralPart, decimalPart, amountUnit, this.currency);
  }

  isZero(): boolean {
    return !Number(this.amount);
  }

  private formatNumber(amount: number) {
    return new Intl.NumberFormat("en", { style: "currency", currency: "USD" }).format(amount).split("$")[1];
  }
}

export class Transaction {
  readonly opportunityImage: string;
  readonly opportunityId: string;
  readonly opportunityTitle: string;
  readonly amount: DollarAmount;
  readonly contractLink: string;
  readonly opportunityType: OpportunityType;
  readonly status: string;
  readonly processedDate: LocalizedDate;

  constructor(transaction) {
    Object.assign(this, transaction);
    this.amount = new DollarAmount(transaction.amount);
    this.processedDate = new LocalizedDate(transaction.invoiceDate);
  }

  processedDateLabel(): string {
    return this.status.toLowerCase() === "pending" ? "-" : this.processedDate.format("MM/DD/YY");
  }
}

export class DefaultPaymentDateRange {
  readonly startDate: LocalizedDate;
  readonly endDate: LocalizedDate;
  private static allTimeRange = 24;

  constructor(paymentLaunchDate: string) {
    const last24Months = LocalizedDate.subtractFromNow(DefaultPaymentDateRange.allTimeRange, "months");
    const launchDate = LocalizedDate.fromFormattedDate(paymentLaunchDate);
    this.startDate = last24Months.isAfter(launchDate) ? last24Months : launchDate;
    this.endDate = LocalizedDate.now();
  }
}
export class PaymentsHistory {
  readonly count: number;
  readonly total: number;
  readonly details: Transaction[];
  readonly totalPaidAmount: DollarAmount;
  readonly totalPendingAmount: DollarAmount;

  constructor(history) {
    Object.assign(this, history);
    this.details = history.details.map((transaction) => new Transaction(transaction));
    this.totalPaidAmount = new DollarAmount(history.totalPaidAmount);
    this.totalPendingAmount = new DollarAmount(history.totalPendingAmount);
  }
}

export type PaymentStatusType = "PAID" | "PENDING" | "ALL";
export type OpportunityType = "marketing_opportunity" | "support_a_creator" | "sims_ugx_opportunity" | "ALL";

export const mapOpportunityTypeForAPI = (opportunityType: OpportunityType): OpportunityType =>
  opportunityType === "sims_ugx_opportunity" ? ("ugx_opportunity" as OpportunityType) : opportunityType;
export type PaymentsCriteriaProperties = {
  status?: PaymentStatusType;
  opportunityType?: OpportunityType;
  page: number;
  size: number;
};

export type PaymentsCriteria = PaymentsCriteriaProperties & {
  startDate: LocalizedDate;
  endDate: LocalizedDate;
};

export type PaymentsPayload = PaymentsCriteriaProperties & {
  startDate: string;
  endDate: string;
};
export class CreatorCode {
  readonly startDate: LocalizedDate;
  readonly endDate: LocalizedDate;
  readonly timeZone: string;
  readonly code: string;

  constructor({ activationWindow, code }) {
    const { startDate, endDate, timeZone } = activationWindow;
    this.startDate = new LocalizedDate(startDate);
    this.endDate = new LocalizedDate(endDate);
    this.timeZone = timeZone;
    this.code = code;
  }
}

export class TransactionWithCreatorCode extends Transaction {
  readonly creatorCode: CreatorCode = null;

  constructor({ creatorCode, ...transaction }) {
    super(transaction);
    if (transaction.opportunityType === "support_a_creator") this.creatorCode = new CreatorCode(creatorCode);
  }

  code(): string {
    return this.creatorCode.code;
  }

  activationPeriod(): string {
    return (
      this.creatorCode.startDate.formatWithEpoch("MM/DD/YY") +
      " - " +
      this.creatorCode.endDate.formatWithEpoch("MM/DD/YY")
    );
  }
}

export class PaymentsHistoryWithCreatorCode {
  readonly count: number;
  readonly total: number;
  readonly details: TransactionWithCreatorCode[];
  readonly totalPaidAmount: DollarAmount;
  readonly totalPendingAmount: DollarAmount;

  constructor(history) {
    Object.assign(this, history);
    this.details = history.details.map(
      (transaction: TransactionWithCreatorCode) => new TransactionWithCreatorCode(transaction)
    );
    this.totalPaidAmount = new DollarAmount(history.totalPaidAmount);
    this.totalPendingAmount = new DollarAmount(history.totalPendingAmount);
  }
}

const getPaymentsHistoryWithCreatorCode = async (
  criteria: PaymentsCriteria
): Promise<AxiosResponse<PaymentsHistoryWithCreatorCode>> => {
  const criteriaPayload = {
    ...criteria,
    ...(criteria.opportunityType && { opportunityType: mapOpportunityTypeForAPI(criteria.opportunityType) }),
    startDate: criteria.startDate.formatWithEpoch("MM/DD/YYYY"),
    endDate: criteria.endDate.formatWithEpoch("MM/DD/YYYY")
  } as PaymentsPayload;
  return await client
    .get("/api/v2/payments-history", { query: criteriaPayload })
    .then((response: AxiosResponse<PaymentsHistoryWithCreatorCode>) => {
      response.data = new PaymentsHistoryWithCreatorCode(response.data);
      return response;
    });
};

const getPaymentsIFrameUrl = async (
  data: PaymentsIFrameUrlPayload
): Promise<AxiosResponse<{ id?: string; embeddableUrl?: string }>> => {
  return (await client.post("/api/payment-information", { body: data })) as AxiosResponse<{
    id?: string;
    embeddableUrl?: string;
  }>;
};

const PaymentsService = { getPaymentsHistoryWithCreatorCode, getPaymentsIFrameUrl };

export default PaymentsService;
