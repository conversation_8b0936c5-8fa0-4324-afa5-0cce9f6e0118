import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { HowItWorksProps } from "pages/how-it-works";
import featureFlags from "utils/feature-flags";

export default class HowItWorksPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<HowItWorksProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<HowItWorksProps>> {
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.defaultAvatar, this.program)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        interestedCreator: featureFlags.isInterestedCreatorFlowEnabled(),
        creatorTypesFallback: config.FALLBACK_CREATOR_TYPES,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "how",
          "creator-type",
          "notifications",
          "connect-accounts",
          "opportunities"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled()
      }
    };
  }
}
