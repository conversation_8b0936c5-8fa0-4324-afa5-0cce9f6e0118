import { AxiosResponse } from "axios";
import { ContentUrlWithoutFollowers } from "../../../pages/interested-creators/information";
import { PreferredFranchise } from "@src/interestedCreators/InterestedCreator";
import client from "./Client";
import { Country, CreatorType } from "@eait-playerexp-cn/metadata-types";
import { PreferredLanguageResponse } from "@src/languages/PreferredLanguage";

export type InterestedCreator = {
  preferredFranchises?: PreferredFranchise[];
  defaultGamerTag: string;
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  originEmail: string;
  dateOfBirth: string | number;
  preferredEmail?: string;
  country?: Country;
  // creatorTypes should be passed as CreatorType[] in the payload of submission(V2/Post);
  // When we get the creatorTypes from CRM, it returns only with values (String[]).
  // Hence, adding a type as CreatorType[] | string[] for creatorTypes.
  creatorTypes?: CreatorType[] | string[];
  contentLanguages?: PreferredLanguageResponse[];
  contentUrls?: ContentUrlWithoutFollowers[];
  preferredLanguage?: PreferredLanguageResponse;
  createdDate?: string;
  canApply?: boolean;
};

const saveInterestedCreatorInformation = async (interestedCreator: InterestedCreator): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/v2/interested-creators", { body: interestedCreator })) as AxiosResponse<void>;
};

const addRequestToJoinFor = async (interestedCreator: InterestedCreator): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/v3/interested-creators", { body: interestedCreator })) as AxiosResponse<void>;
};

const saveApplication = async (interestedCreator: InterestedCreator): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/applications", { body: interestedCreator })) as AxiosResponse<void>;
};

const InterestedCreatorsService = {
  saveInterestedCreatorInformation,
  addRequestToJoinFor,
  saveApplication
};

export default InterestedCreatorsService;
