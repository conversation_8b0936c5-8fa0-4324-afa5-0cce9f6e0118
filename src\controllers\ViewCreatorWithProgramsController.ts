import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import CreatorsWithProgramsHttpClient from "../creators/CreatorsWithProgramsHttpClient";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "../../config";

@Service()
class ViewCreatorWithProgramController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly creators: CreatorsWithProgramsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);

    const creator = await this.creators.withId(user.id);

    this.json(res, creator);
  }
}

export default ViewCreatorWithProgramController;
