import { AxiosResponse } from "axios";
import client from "./Client";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

type SignedTermsAndConditions = {
  readonly creatorId: string;
  readonly termsAndConditionsId: string;
  acceptedOnDate: LocalizedDate;
  readonly documentUrl: string;
  readonly content: string;
};

type SignedContract = {
  uploadedByName: string;
  opportunityName: string;
  label: string;
  signedOnDate: LocalizedDate;
  documentUrl: string;
  uploaded: boolean;
};

export type SignedLegalDocuments = {
  readonly contracts: SignedContract[];
  readonly history: SignedTermsAndConditions[];
};

const geDocumentsWithSignature = async (): Promise<AxiosResponse<SignedLegalDocuments>> => {
  return await client.get("/api/v2/legal-documents").then((response: AxiosResponse<SignedLegalDocuments>) => {
    response.data.contracts.map((contract: SignedContract) => {
      contract.signedOnDate = new LocalizedDate(contract.signedOnDate as unknown as number); // we are getting 'signedOnDate' as number from backend
      return contract;
    });
    response.data.history.map((termsAndConditions: SignedTermsAndConditions) => {
      termsAndConditions.acceptedOnDate = new LocalizedDate(termsAndConditions.acceptedOnDate as unknown as number); // we are getting 'acceptedOnDate' as number from backend
      return termsAndConditions;
    });
    return response;
  });
};

const LegalDocumentsService = { geDocumentsWithSignature };

export default LegalDocumentsService;
