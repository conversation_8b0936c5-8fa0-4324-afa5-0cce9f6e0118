import { NextApiResponse } from "next";
import { ampli } from "../../analytics/server/src/ampli";
import config from "../../config";
import { NextHandler } from "next-connect";
import { NextApiRequestWithSession, NextMiddleware } from "@eait-playerexp-cn/server-kernel";

const initializeAnalytics: NextMiddleware<NextApiRequestWithSession, NextApiResponse, () => void> = async (
  _req: NextApiRequestWithSession,
  _res: NextApiResponse,
  next: NextHandler
): Promise<void> => {
  ampli.load({
    client: {
      apiKey: config.AMPLITUDE_API_KEY
    }
  });
  await next();
};

export default initializeAnalytics;
