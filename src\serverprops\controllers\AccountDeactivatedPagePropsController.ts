import {
  NextApiRequestWithSession,
  Request<PERSON><PERSON><PERSON>,
  Request<PERSON><PERSON>lerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { AccountDeactivatedProps } from "pages/account-deactivated";

export default class AccountDeactivatedPagePropsController
  extends RequestH<PERSON>ler
  implements ServerPropsController<AccountDeactivatedProps>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<AccountDeactivatedProps>> {
    if (!this.hasSession(req, `${this.program}.deactivatedAccount`)) return { notFound: true };
    const deactivatedAccount = this.hasSession(req, `${this.program}.deactivatedAccount`)
      ? (this.session(req, `${this.program}.deactivatedAccount`) as { defaultGamerTag: string })
      : { defaultGamerTag: "" };
    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : false;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        ...(await serverSideTranslations(this.currentLocale, ["common", "account-deactivated"])),
        showInitialMessage,
        deactivatedAccount
      }
    };
  }
}
