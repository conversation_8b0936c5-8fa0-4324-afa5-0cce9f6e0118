import CreatorWithCreatorCode from "./CreatorWithCreatorCode";
import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithCreatorCodeHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithCreatorCode> {
    const response = (await this.client.get(`/v4/creators/${id}`)) as AxiosResponse;
    return Promise.resolve(CreatorWithCreatorCode.fromApi(response.data));
  }
}

export default CreatorsWithCreatorCodeHttpClient;
