import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import ApplicationAccepted from "../../../pages/interested-creators/application-accepted";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { ApplicationAcceptedPageLabels } from "@src/contentManagement/ApplicationAcceptedPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsApplicationAccepted", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const analytics = {
    checkedApplicationStatus: jest.fn()
  } as unknown as <PERSON>rowserAnalytics;

  const basePageLabels = {
    applicationAcceptedLabels: {
      pageTitle: "Application Accepted",
      title: "Congratulations!",
      description: "Your application has been accepted",
      buttonText: "Get Started",
      descriptionPara1: "Para 1",
      descriptionPara2: "Para 2",
      descriptionPara3: "Para 3",
      returnToCreatorNetwork: "Return",
      completeYourProfile: "Complete Profile"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationAcceptedPageLabels & CommonPageLabels;

  const applicationAcceptedProps = {
    pageLabels: basePageLabels,
    locale,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
  });

  it("shows tab title as 'Application Accepted'", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(document.title).toMatch(/Application Accepted/);
  });

  it("shows remote accepted component", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with navigation callback configured", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");

    expect(dynamicComponent).toBeInTheDocument();
  });

  it("shows component with default analytics when not provided", () => {
    const propsWithoutAnalytics = {
      ...applicationAcceptedProps,
      analytics: undefined
    };

    expect(() => render(<ApplicationAccepted {...propsWithoutAnalytics} />)).not.toThrow();
  });

  it("shows header with correct labels", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });

  it("shows component with document title configuration", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows correct props to ApplicationAcceptedPage component", async () => {
    render(<ApplicationAccepted {...applicationAcceptedProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");

    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("shows component with different locale", async () => {
    const propsWithDifferentLocale = {
      ...applicationAcceptedProps,
      locale: "fr-fr"
    };

    render(<ApplicationAccepted {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with minimal page labels", () => {
    const propsWithMinimalLabels = {
      ...applicationAcceptedProps,
      pageLabels: {
        applicationAcceptedLabels: {
          pageTitle: "Test Title"
        },
        commonPageLabels: {
          creatorNetwork: "Network",
          close: "Close"
        }
      } as unknown as ApplicationAcceptedPageLabels & CommonPageLabels
    };

    expect(() => render(<ApplicationAccepted {...propsWithMinimalLabels} />)).not.toThrow();
  });
});
