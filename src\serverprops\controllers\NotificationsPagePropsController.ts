import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import ContentManagementService from "@src/api/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { NotificationsProps } from "pages/notifications";
import featureFlags from "utils/feature-flags";

export default class NotificationsPagePropController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<NotificationsProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<NotificationsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const pageLabels = (await this.contents.getPageLabels(this.currentLocale, "notifications")) as {
      notificationsPageLabels: Record<string, unknown>;
      notificationsBellLabels: Record<string, unknown>;
    };

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "notifications",
          "connect-accounts",
          "opportunities"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        pageLabels
      }
    };
  }
}
