import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import config from "config";

const runtimeConfiguration = (user?: AuthenticatedUser) => ({
  GTM_AUTH: config.GTM_AUTH,
  GTM_PREVIEW: config.GTM_PREVIEW,
  AMPLITUDE_API_KEY: config.AMPLITUDE_API_KEY,
  AMPLITUDE_ENV: config.AMPLITUDE_ENV,
  BUILD_VERSION: config.RELEASE_VERSION,
  SENTRY_DSN: config.SENTRY_DSN,
  APP_ENV: config.APP_ENV,
  INITIAL_MESSAGE_TITLE: config.INITIAL_MESSAGE_TITLE,
  INITIAL_MESSAGE_DESCRIPTION: config.INITIAL_MESSAGE_DESCRIPTION,
  SUPPORTED_LOCALES: config.SUPPORTED_LOCALES,
  ANALYTICS_SAMPLE_RATE: config.ANALYTICS_SAMPLE_RATE,
  HTTP_REQUEST_TIMEOUT: config.HTTP_REQUEST_TIMEOUT,
  FLAG_OBSERVABILITY: config.FLAG_OBSERVABILITY,
  METADATA_API_BASE_URL: config.METADATA_API_BASE_URL,
  CREATORS_API_BASE_URL: config.CREATORS_API_BASE_URL,
  SERVICE_NAME: config.SERVICE_NAME,
  NOTIFICATIONS_MFE_BASE_URL: config.NOTIFICATIONS_MFE_BASE_URL,
  PROGRAM_CODE: config.PROGRAM_CODE,
  FLAG_SIGNED_URL_V1_ENABLED: config.FLAG_SIGNED_URL_V1_ENABLED,
  FLAG_SIGNED_URL_V2_ENABLED: config.FLAG_SIGNED_URL_V2_ENABLED,
  MENU_ITEMS: config.MENU_ITEMS,
  NOTIFICATION_BASE_URLS: config.NOTIFICATION_BASE_URLS,
  SINGLE_PROGRAM_NOTIFICATIONS: config.SINGLE_PROGRAM_NOTIFICATIONS,
  DEFAULT_NOTIFICATION_PROGRAM: config.DEFAULT_NOTIFICATION_PROGRAM,
  FLAG_NEW_NAVIGATION_ENABLED: config.FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_CREATORS_API_WITH_PROGRAM: config.FLAG_CREATORS_API_WITH_PROGRAM,
  user: config.FLAG_CREATORS_API_WITH_PROGRAM && user ? user : null,
  FLAG_CONTENT_WITH_FINAL_REMARK: config.FLAG_CONTENT_WITH_FINAL_REMARK,
  FLAG_OPPORTUNITIES_BY_STATUS_WITH_PROGRAM: config.FLAG_OPPORTUNITIES_BY_STATUS_WITH_PROGRAM,
  FLAG_SUBMITTED_CONTENT_WITH_PROGRAM: config.FLAG_SUBMITTED_CONTENT_WITH_PROGRAM,
  FLAG_PER_PROGRAM_PROFILE: config.FLAG_PER_PROGRAM_PROFILE,
  DEFAULT_AVATAR_IMAGE: config.DEFAULT_AVATAR_IMAGE,
  APPLICATIONS_MFE_BASE_URL: config.APPLICATIONS_MFE_BASE_URL
});

export default runtimeConfiguration;
