import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import NoAccountPagePropsController from "./controllers/NoAccountPagePropsController";
import ContentManagementService from "@src/api/services/ContentManagementService";

const noAccountPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new NoAccountPagePropsController(ApiContainer.get("options"), ApiContainer.get(ContentManagementService), locale)
  );

export default noAccountPageProps;
