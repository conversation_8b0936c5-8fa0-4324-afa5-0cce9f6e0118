import "reflect-metadata";
import { useTranslation } from "next-i18next";
import { memo, useMemo } from "react";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import withRegisteredUser from "../src/utils/WithRegisteredUser";
import RedirectException from "../src/utils/RedirectException";
import withTermsAndConditionsUpToDate from "../src/utils/WithTermsAndConditionsUpToDate";
import labelsCommon from "../config/translations/common";
import BrowserAnalytics, { AuthenticatedUser, AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import {
  labelsPaymentBanner,
  labelsPaymentDetails,
  labelsPaymentHistoryGrid,
  labelsPaymentInformation
} from "@config/translations/payment-information";
import labelsPaymentsFilter from "@config/translations/payments-filter";
import PaymentInformationPage from "@components/pages/payment-information/PaymentInformationPage";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import Footer from "@components/footer/ProgramFooter";
import Header from "@components/header/Header";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "../config";
import flags from "../utils/feature-flags";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import { GetServerSidePropsResult } from "next";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import paymentInformationProps from "@src/serverprops/PaymentInformationProps";

export type PaymentInformationProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  analytics?: BrowserAnalytics;
  CN_LAUNCH_DATE: string;
  locale: string;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
};

export default memo(function PaymentInformation({
  user,
  analytics = new BrowserAnalytics(user),
  CN_LAUNCH_DATE,
  locale,
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_NEW_FOOTER_ENABLED
}: PaymentInformationProps) {
  const { t } = useTranslation(["common", "payment-information", "payments-filter", "notifications"]);
  const { layout, labels, notificationsLabels } = useMemo(() => {
    const commonLabels = labelsCommon(t);
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      layout: commonLabels,
      labels: {
        paymentInfoLabels: {
          ...labelsPaymentInformation(t),
          buttons: {
            next: commonLabels.buttons.next,
            prev: commonLabels.buttons.prev,
            close: commonLabels.buttons.close
          }
        },
        paymentBannerLabels: labelsPaymentBanner(t),
        paymentDetailsLabels: labelsPaymentDetails(t),
        paymentHistoryGridLabels: labelsPaymentHistoryGrid(t),
        paymentFilterLabels: {
          ...labelsPaymentsFilter(t),
          buttons: { ok: commonLabels.buttons.ok, cancel: commonLabels.buttons.cancel },
          header: { calendar: commonLabels.header.calendar }
        },
        buttonLabel: commonLabels.buttons.remove
      }
    };
    return { layout: commonLabels, ...labels, notificationsLabels: notificationBellLabels };
  }, [t]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const footerLabels = layout.footer;

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.paymentInformation}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={null}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody className="payment-information-container" showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
        <PaymentInformationPage
          {...{
            labels,
            user,
            unhandledError: layout.main.unhandledError,
            CN_LAUNCH_DATE,
            analytics,
            FLAG_NEW_NAVIGATION_ENABLED
          }}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          locale={locale}
          labels={footerLabels}
          analytics={analytics}
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
        />
      </LayoutFooter>
    </Layout>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(paymentInformationProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<PaymentInformationProps>;
  }

  let user;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const authenticatedUser = user ? AuthenticatedUserFactory.fromSession(user, flags.isCreatorsAPIWithProgram()) : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      locale,
      ...(await serverSideTranslations(locale, ["common", "payment-information", "payments-filter", "notifications"])),
      CN_LAUNCH_DATE: config.CN_LAUNCH_DATE,
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled()
    }
  };
};
