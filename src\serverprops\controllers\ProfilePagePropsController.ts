import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { ProfilePageProps } from "../ProfileProps";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { FacebookPages } from "@src/accounts/ConnectedAccountsHttpClient";
import { OAuthError } from "@src/controllers/ConnectTikTokAccountController";
import featureFlags from "../../../utils/feature-flags";
import config from "config";

export default class ProfilePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ProfilePageProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<ProfilePageProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};
    const error = this.hasSession(req, "error") ? (this.session(req, "error") as OAuthError) : null;
    const invalidTikTokScope = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : false;

    await this.removeFromSession(req, "error");
    await this.removeFromSession(req, "INVALID_TIKTOK_SCOPE");

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pages,
        error,
        invalidTikTokScope,
        user: authenticatedUser,
        CN_LAUNCH_DATE: config.CN_LAUNCH_DATE,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "profile",
          "franchises-you-play",
          "information",
          "connect-accounts",
          "communication-preferences",
          "legal-documents",
          "creator-type",
          "payment-information",
          "point-of-contact",
          "notifications",
          "opportunities",
          "payments-filter"
        ])),
        FLAG_COUNTRIES_BY_TYPE: featureFlags.isCountriesByTypeEnabled(),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled()
      }
    };
  }
}
