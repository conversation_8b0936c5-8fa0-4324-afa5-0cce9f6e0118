import { AxiosResponse } from "axios";
import client from "./Client";
import FutureCreator from "@src/notifications/FutureCreator";

/** @deprecated This method won't be needed once the Network is publicly available */
const notifyMe = async (futureCreator: FutureCreator): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/notifications", { body: futureCreator })) as AxiosResponse<void>;
};

const NotificationsService = {
  notifyMe
};

export default NotificationsService;
