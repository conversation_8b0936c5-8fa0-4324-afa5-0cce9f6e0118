export default function labelsCommunicationPreferences(t) {
  return {
    title: t("communication-preferences:title"),
    profileTitle: t("communication-preferences:profileTitle"),
    description: t("communication-preferences:description"),
    modalConfirmationTitle: t("common:modalConfirmationTitle"),
    confirmationDesc1: t("common:confirmationDesc1"),
    confirmationDesc2: t("common:confirmationDesc2"),
    success: {
      updatedInformationHeader: t("communication-preferences:success.updatedInformationHeader"),
      preferredEmail: t("communication-preferences:success.preferredEmail"),
      preferredPhoneNumber: t("communication-preferences:success.preferredPhoneNumber"),
      preferredLanguage: t("communication-preferences:success.preferredLanguage"),
      contentLanguage: t("communication-preferences:success.contentLanguage")
    },
    messages: {
      preferredEmail: t("communication-preferences:messages.preferredEmail"),
      preferredEmailTooLong: t("communication-preferences:messages.preferredEmailTooLong"),
      preferredEmailInvalid: t("communication-preferences:messages.preferredEmailInvalid"),
      preferredPhoneNumber: t("communication-preferences:messages.preferredPhoneNumber"),
      preferredPhoneNumberTooLong: t("communication-preferences:messages.preferredPhoneNumberTooLong"),
      contentLanguage: t("communication-preferences:messages.contentLanguage"),
      language: t("communication-preferences:messages.language")
    },
    labels: {
      addDiscord: t("communication-preferences:labels.addDiscord"),
      discordTitle: t("communication-preferences:labels.discordTitle"),
      discordDescription: t("communication-preferences:labels.discordDescription"),
      preferredEmailAddressTitle: t("communication-preferences:labels.preferredEmailAddressTitle"),
      preferredEmailAddressDescription: t("communication-preferences:labels.preferredEmailAddressDescription"),
      preferredEmail: t("communication-preferences:labels.preferredEmail"),
      preferredPhoneNumberTitle: t("communication-preferences:labels.preferredPhoneNumberTitle"),
      preferredPhoneNumber: t("communication-preferences:labels.preferredPhoneNumber"),
      contentLanguagesTitle: t("communication-preferences:labels.contentLanguagesTitle"),
      contentLanguagesDescription: t("communication-preferences:labels.contentLanguagesDescription"),
      contentLanguage: t("communication-preferences:labels.contentLanguage"),
      languageTitle: t("communication-preferences:labels.languageTitle"),
      languageDescription: t("communication-preferences:labels.languageDescription"),
      language: t("communication-preferences:labels.language")
    }
  };
}
