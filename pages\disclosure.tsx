import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useEffect, useMemo } from "react";
import labelsCommon from "../config/translations/common";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import labelsDisclosure from "../config/translations/disclosure";
import withUserSession from "../src/utils/WithUserSession";
import { DisclosurePage } from "@components/pages/DisclosurePage";
import BrowserAnalytics, { AuthenticatedUser } from "../src/analytics/BrowserAnalytics";
import flags from "../utils/feature-flags";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import Footer from "../components/footer/ProgramFooter";
import featureFlags from "../utils/feature-flags";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import disclosureProps from "@src/serverprops/DisclosureProps";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";

export type DisclosureProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  interestedCreator: boolean;
  analytics?: BrowserAnalytics;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
};

export default memo(function Disclosure({
  user,
  interestedCreator,
  FLAG_NEW_FOOTER_ENABLED,
  FLAG_NEW_NAVIGATION_ENABLED,
  analytics = new BrowserAnalytics()
}: DisclosureProps) {
  const { locale, pathname } = useRouter();
  const { t } = useTranslation(["common", "disclosure", "notifications", "connect-accounts", "opportunities"]);
  const { layout, disclosure, notificationsLabels } = useMemo(() => {
    const common = labelsCommon(t);
    const notificationBellLabels = mapNotificationsBellLabels(t);
    return {
      layout: {
        ...common,
        footer: { locale: locale, labels: common.footer },
        pageTitle: common.header.disclosure
      },
      disclosure: labelsDisclosure(t),
      notificationsLabels: notificationBellLabels
    };
  }, [locale, t]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const footerLabels = layout.footer.labels;

  useEffect(() => {
    if (user) {
      analytics.viewedMarketingPage({ locale, page: pathname });
    }
  }, [locale, pathname]);

  return (
    <Layout>
      <div className="layout-disclosure-wrapper">
        <div className="layout-disclosure-content">
          <LayoutHeader pageTitle={layout.pageTitle}>
            <Header
              {...headerLabels}
              user={user}
              notificationsLabels={notificationsLabels}
              analytics={analytics}
              interestedCreator={interestedCreator}
              FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
            />
          </LayoutHeader>
          <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
            <DisclosurePage {...disclosure} />
          </LayoutBody>
        </div>
        <LayoutFooter>
          <Footer
            FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
            locale={locale}
            labels={footerLabels}
            analytics={analytics}
          />
        </LayoutFooter>
      </div>
    </Layout>
  );
});

type DisclosurePage = {
  interestedCreator: boolean;
  user: AuthenticatedUser | null;
};

export const getServerSideProps: GetServerSideProps<DisclosurePage> = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(disclosureProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<DisclosureProps>;
  }

  const user = await withUserSession(req, res);
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      interestedCreator: flags.isInterestedCreatorFlowEnabled(),
      ...(await serverSideTranslations(locale, [
        "common",
        "disclosure",
        "notifications",
        "connect-accounts",
        "opportunities"
      ]))
    }
  };
};
