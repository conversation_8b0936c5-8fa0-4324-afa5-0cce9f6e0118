import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ApplicationPendingPagePropsController from "./controllers/ApplicationPendingPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import ContentManagementService from "@src/api/services/ContentManagementService";

const applicationPendingPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new ApplicationPendingPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient)
    )
  );

export default applicationPendingPageProps;
