import "reflect-metadata";
import React, { ComponentType, memo, useCallback } from "react";
import flags from "../../utils/feature-flags";
import withInterestedCreator from "../../src/utils/WithInterestedCreator";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import withLocalizedUrl from "../../src/utils/WithLocalizedUrl";
import { InterestedCreator } from "@src/api/services/InterestedCreatorsServices";
import InterestedCreatorHeader from "../../components/pages/interested-creators/InterestedCreatorHeader";
import Layout, { LayoutBody } from "../../components/Layout";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import applicationCompletePageProps from "@src/serverprops/ApplicationCompletePageProps";
import Loading from "@components/Loading";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/api/services/ContentManagementService";
import { ApplicationCompletePageLabels } from "@src/contentManagement/ApplicationCompletePageMapper";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

const ApplicationCompletedPage: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "applications/ApplicationCompletedPage"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type CompletePageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  locale: string;
  interestedCreator: InterestedCreator;
  pageLabels: ApplicationCompletePageLabels & CommonPageLabels;
};

export default memo(function Complete({
  interestedCreator,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  pageLabels
}: CompletePageProps) {
  const router = useRouter();
  const { applicationCompletePageLabels, commonPageLabels } = pageLabels;
  const { creatorNetwork, close } = commonPageLabels;

  const logout = useCallback(() => {
    router.push("/api/logout");
  }, [router]);

  return (
    <Layout>
      <LayoutBody className="interested-creator-layout">
        <div className="mg-container">
          <InterestedCreatorHeader {...{ logoLabel: creatorNetwork, closeButtonAriaLabel: close }} />
          <div className="mg-bg"> </div>
          <ApplicationCompletedPage
            labels={applicationCompletePageLabels}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={interestedCreator?.createdDate}
            emailId={interestedCreator?.originEmail}
            onBackButtonClick={logout}
            applicationCompleteThumbnail={"/img/home-header--980w-x-690h.png"}
          />
        </div>
      </LayoutBody>
    </Layout>
  );
});

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(applicationCompletePageProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<CompletePageProps>;
  }

  const interestedCreator = await withInterestedCreator(req, res, null, locale);
  if (!flags.isInterestedCreatorFlowEnabled() || !interestedCreator) {
    const urlLocale = withLocalizedUrl(req, locale);
    return {
      redirect: {
        destination: urlLocale,
        statusCode: 302
      }
    };
  }
  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "applicationComplete");

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(),
      interestedCreator,
      locale,
      pageLabels,
      INTERESTED_CREATOR_REAPPLY_PERIOD: flags.isInterestedCreatorReApplyEnabled()
    }
  };
};
