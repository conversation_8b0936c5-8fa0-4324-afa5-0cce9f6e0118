import { DEFAULT_LOCALE, getLocale } from "../../utils";
import config from "../../config";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";

/**
 * @deprecated
 */
export default function withLocalizedUrl(req: NextApiRequestWithSession, locale?: string): string {
  locale = locale || req.cookies.NEXT_LOCALE || getLocale(req.headers, config.SUPPORTED_LOCALES);
  return locale !== DEFAULT_LOCALE ? `/${locale}/` : "/";
}
