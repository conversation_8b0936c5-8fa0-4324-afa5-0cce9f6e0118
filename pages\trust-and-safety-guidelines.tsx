import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useEffect, useMemo } from "react";
import labelsCommon from "../config/translations/common";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import labelsTrustAndSafetyGuidelines from "../config/translations/trust-and-safety-guidelines";
import withUserSession from "../src/utils/WithUserSession";
import BrowserAnalytics, { AuthenticatedUser, AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import flags from "../utils/feature-flags";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import Footer from "../components/footer/ProgramFooter";
import TrustAndSafetyGuidelinesPage from "../components/pages/trust-and-safety-guidelines/TrustAndSafetyGuidelinesPage";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import { GetServerSidePropsResult } from "next";
import trustAndSafetyGuidelinesProps from "@src/serverprops/TrustAndSafetyGuidelinesProps";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";

export type TrustAndSafetyGuidelinesProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  interestedCreator: boolean;
  analytics?: BrowserAnalytics;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
};

export default memo(function Disclosure({
  user,
  interestedCreator,
  analytics = new BrowserAnalytics(),
  FLAG_NEW_FOOTER_ENABLED,
  FLAG_NEW_NAVIGATION_ENABLED
}: TrustAndSafetyGuidelinesProps) {
  const { locale, pathname } = useRouter();
  const { t } = useTranslation(["common", "disclosure", "notifications", "connect-accounts", "opportunities"]);
  const { layout, trustAndSafetyGuidelines, notificationsLabels } = useMemo(() => {
    const common = labelsCommon(t);
    const notificationBellLabels = mapNotificationsBellLabels(t);
    return {
      layout: { ...common },
      trustAndSafetyGuidelines: labelsTrustAndSafetyGuidelines(t),
      notificationsLabels: notificationBellLabels
    };
  }, [t]);

  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };

  useEffect(() => {
    if (user) {
      analytics.viewedMarketingPage({ locale, page: pathname });
    }
  }, [analytics, locale, pathname, user]);

  return (
    <Layout>
      <LayoutHeader pageTitle={trustAndSafetyGuidelines.title}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={interestedCreator}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
        <TrustAndSafetyGuidelinesPage labels={trustAndSafetyGuidelines} />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={locale}
          labels={layout.footer}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(trustAndSafetyGuidelinesProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<TrustAndSafetyGuidelinesProps>;
  }

  const user = await withUserSession(req, res);
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      interestedCreator: flags.isInterestedCreatorFlowEnabled(),
      ...(await serverSideTranslations(locale, [
        "common",
        "trust-and-safety-guidelines",
        "notifications",
        "connect-accounts",
        "opportunities"
      ])),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled()
    }
  };
};
