import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import BrowserAnalytics, { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type InformationProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  registrationCode?: string | null;
  analytics?: BrowserAnalytics;
  futureCreator: boolean;
  showInitialMessage?: boolean;
  FLAG_COUNTRIES_BY_TYPE: boolean;
  FLAG_ONBOARDING_CUSTOM_LINKS: boolean;
};

export default class InformationPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<InformationProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<InformationProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );
    const futureCreator = this.hasSession(req, `${this.program}.futureCreator`)
      ? (this.session(req, `${this.program}.futureCreator`) as boolean)
      : null;
    const showInitialMessage = this.hasSession(req, `${this.program}.showInitialMessage`)
      ? (this.session(req, `${this.program}.showInitialMessage`) as boolean)
      : null;
    const registrationCode = this.hasSession(req, `${this.program}.registrationCode`)
      ? (this.session(req, `${this.program}.registrationCode`) as string)
      : null;
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        futureCreator,
        showInitialMessage,
        user: authenticatedUser,
        registrationCode,
        FLAG_COUNTRIES_BY_TYPE: featureFlags.isCountriesByTypeEnabled(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "breadcrumb",
          "information",
          "opportunities",
          "add-content"
        ])),
        FLAG_ONBOARDING_CUSTOM_LINKS: featureFlags.isOnboardingCustomLinksEnabled()
      }
    };
  }
}
