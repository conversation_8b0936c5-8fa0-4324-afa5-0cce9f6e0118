import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { DisclosureProps } from "../../../pages/disclosure";
import featureFlags from "utils/feature-flags";

export default class DisclosurePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<DisclosureProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<DisclosureProps>> {
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.defaultAvatar, this.program)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        interestedCreator: featureFlags.isInterestedCreatorFlowEnabled(),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "disclosure",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
