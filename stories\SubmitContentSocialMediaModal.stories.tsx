import React, { useState } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import SubmitSocialMediaContentModal, {
  SubmitSocialMediaContentModalProps
} from "../components/pages/content-submission/SubmitSocialMediaContentModal";

const meta: Meta<typeof SubmitSocialMediaContentModal> = {
  title: "Creator Network/Pages/Content Submission/Submit Social Media Content Modal",
  component: SubmitSocialMediaContentModal
};

export default meta;

type Story = StoryObj<typeof SubmitSocialMediaContentModal>;

const render = (args: SubmitSocialMediaContentModalProps) => {
  const [show, setShow] = useState(false);
  const showModal = () => {
    setShow(true);
  };
  const closeModal = () => {
    setShow(false);
  };

  return (
    <div>
      <button className="btn btn-primary" onClick={showModal}>
        Show
      </button>
      {show && (
        <SubmitSocialMediaContentModal
          {...args}
          {...{
            show,
            onClose: () => closeModal(),
            buttonLabels: { cancel: "Cancel", submit: "Submit", close: "Close" },
            title: `Add content from ${args.mediaType}`
          }}
        />
      )}
    </div>
  );
};

export const YouTube: Story = {
  args: {
    mediaType: "YouTube",
    name: "Hari",
    formLabels: {
      contentUrl: "Please enter the YouTube URL:",
      contentUrlPlaceholder: "Example: https://www.youtube.com/mylink"
    },
    infoLabel: "Please ensure that the URL you are adding is from this YouTube account:",
    errorLabels: {
      duplicateUrl: "This URL has already been submitted.",
      instagramErrorUrl:
        "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.",
      videoNotFromChannel: "This video is not from a social channel connected to your CN account.",
      youtubeVideoError: "No YouTube video found with given ID.",
      genericContentError: "Please enter a valid URL.",
      unsupportedContentError: "This content which you are trying to submit doesn't match your selected option",
      unsafeUrlError: "You can not submit content from this website or domain.",
      contentUrlRequired: "YouTube URL is required",
      urlNotFromConnectedAccount: 'The URL you entered is not from this connected "Social Channel" account.',
      urlNotFromConnectedChannel: "The URL you entered is not from this connected channel.",
      unknownTikTokVideo: "Submitted TikTok video is either private or has been removed.",
      invalidSocialSubmission: "This URL is not from a supported Social Media Network.",
      accountAuthorizationFailure: "Couldn't complete authorization to submit your content.",
      cannotExpandUrl: "Expanding your short URL is taking longer than usual. Please try again later.",
      invalidFacebookPage: "The URL you're trying to submit doesn't belong to the page you selected.",
      unSupportedContentType: "The type of content being submitted is not currently supported.",
      unSupportedContentTypeForMedia:
        "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.",
      cannotSubmitContentInvalidInput: "Submitted URL is invalid",
      duplicateScannedContentUrl: "Your Community Manager has submitted this URL."
    }
  },
  render
};

export const Twitch: Story = {
  args: {
    mediaType: "Twitch",
    name: "Hari",
    formLabels: {
      contentUrl: "Please enter the Twitch URL:",
      contentUrlPlaceholder: "Example: https://www.twitch.tv/mylink"
    },
    infoLabel: "Please ensure that the URL you are adding is from this Twitch account:",
    errorLabels: {
      duplicateUrl: "This URL has already been submitted.",
      instagramErrorUrl:
        "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.",
      videoNotFromChannel: "This video is not from a social channel connected to your CN account.",
      youtubeVideoError: "No YouTube video found with given id.",
      genericContentError: "Please enter a valid URL.",
      unsupportedContentError: "This content which you are trying to submit doesn't match your selected option",
      unsafeUrlError: "You can not submit content from this website or domain.",
      contentUrlRequired: "Twitch URL is required",
      urlNotFromConnectedAccount: 'The URL you entered is not from this connected "Social Channel" account.',
      urlNotFromConnectedChannel: "The URL you entered is not from this connected channel.",
      unknownTikTokVideo: "Submitted TikTok video is either private or has been removed.",
      invalidSocialSubmission: "This URL is not from a supported Social Media Network.",
      accountAuthorizationFailure: "Couldn't complete authorization to submit your content.",
      cannotExpandUrl: "Expanding your short URL is taking longer than usual. Please try again later.",
      invalidFacebookPage: "The URL you're trying to submit doesn't belong to the page you selected.",
      unSupportedContentType: "The type of content being submitted is not currently supported.",
      unSupportedContentTypeForMedia:
        "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.",
      cannotSubmitContentInvalidInput: "Submitted URL is invalid",
      duplicateScannedContentUrl: "Your Community Manager has submitted this URL."
    }
  },
  render
};

export const Instagram: Story = {
  args: {
    mediaType: "Instagram",
    name: "Hari",
    formLabels: {
      contentUrl: "Please enter the Instagram URL:",
      contentUrlPlaceholder: "Example: https://www.instagram.com/mylink"
    },
    infoLabel: "Please ensure that the URL you are adding is from this Instagram account:",
    errorLabels: {
      duplicateUrl: "This URL has already been submitted.",
      instagramErrorUrl:
        "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.",
      videoNotFromChannel: "This video is not from a social channel connected to your CN account.",
      youtubeVideoError: "No YouTube video found with given id.",
      genericContentError: "Please enter a valid URL.",
      unsupportedContentError: "This content which you are trying to submit doesn't match your selected option",
      unsafeUrlError: "You can not submit content from this website or domain.",
      contentUrlRequired: "Instagram URL is required",
      urlNotFromConnectedAccount: 'The URL you entered is not from this connected "Social Channel" account.',
      urlNotFromConnectedChannel: "The URL you entered is not from this connected channel.",
      unknownTikTokVideo: "Submitted TikTok video is either private or has been removed.",
      invalidSocialSubmission: "This URL is not from a supported Social Media Network.",
      accountAuthorizationFailure: "Couldn't complete authorization to submit your content.",
      cannotExpandUrl: "Expanding your short URL is taking longer than usual. Please try again later.",
      invalidFacebookPage: "The URL you're trying to submit doesn't belong to the page you selected.",
      unSupportedContentType: "The type of content being submitted is not currently supported.",
      unSupportedContentTypeForMedia:
        "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.",
      cannotSubmitContentInvalidInput: "Submitted URL is invalid",
      duplicateScannedContentUrl: "Your Community Manager has submitted this URL."
    }
  },
  render
};

export const Facebook: Story = {
  args: {
    mediaType: "Facebook",
    name: "Hari",
    formLabels: {
      contentUrl: "Please enter the Facebook URL:",
      contentUrlPlaceholder: "Example: https://www.facebook.com/mylink"
    },
    infoLabel: "Please ensure that the URL you are adding is from this Facebook account:",
    errorLabels: {
      duplicateUrl: "This URL has already been submitted.",
      instagramErrorUrl:
        "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.",
      videoNotFromChannel: "This video is not from a social channel connected to your CN account.",
      youtubeVideoError: "No YouTube video found with given id.",
      genericContentError: "Please enter a valid URL.",
      unsupportedContentError: "This content which you are trying to submit doesn't match your selected option",
      unsafeUrlError: "You can not submit content from this website or domain.",
      contentUrlRequired: "Facebook URL is required",
      urlNotFromConnectedAccount: 'The URL you entered is not from this connected "Social Channel" account.',
      urlNotFromConnectedChannel: "The URL you entered is not from this connected channel.",
      unknownTikTokVideo: "Submitted TikTok video is either private or has been removed.",
      invalidSocialSubmission: "This URL is not from a supported Social Media Network.",
      accountAuthorizationFailure: "Couldn't complete authorization to submit your content.",
      cannotExpandUrl: "Expanding your short URL is taking longer than usual. Please try again later.",
      invalidFacebookPage: "The URL you're trying to submit doesn't belong to the page you selected.",
      unSupportedContentType: "The type of content being submitted is not currently supported.",
      unSupportedContentTypeForMedia:
        "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.",
      cannotSubmitContentInvalidInput: "Submitted URL is invalid",
      duplicateScannedContentUrl: "Your Community Manager has submitted this URL."
    }
  },
  render
};
