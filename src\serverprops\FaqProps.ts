import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import FaqPagePropsController from "./controllers/FaqPagePropsController";
import config from "config";

const faqProps = (locale: string) =>
  serverPropsControllerFactory(
    new FaqPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default faqProps;
