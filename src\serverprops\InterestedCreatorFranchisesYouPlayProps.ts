import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import InterestedCreatorFranchisesYouPlayPagePropsController from "./controllers/InterestedCreatorFranchisesYouPlayPagePropsController";
import InterestedCreatorApplicationsHttpClient from "@src/interestedCreators/InterestedCreatorApplicationsHttpClient";
import { interestedCreatorPages } from "pages/interested-creators/information";
import ContentManagementService from "@src/api/services/ContentManagementService";

const interestedCreatorFranchisesYouPlayProps = (locale: string) =>
  serverPropsControllerFactory(
    new InterestedCreatorFranchisesYouPlayPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      ApiContainer.get(InterestedCreatorApplicationsHttpClient),
      interestedCreatorPages.franchises
    )
  );

export default interestedCreatorFranchisesYouPlayProps;
