import AuthenticateCreatorInput from "./AuthenticateCreatorInput";
import EAToken from "../identity/EAToken";
import CreatorCriteria from "../creators/CreatorCriteria";
import UnknownCreator from "../creators/UnknownCreator";
import Identity from "../authentication/Identity";
import AuthenticationProvider from "@src/identity/AuthenticationProvider";
import CreatorsHttpClient from "@src/creators/CreatorsHttpClient";
import config from "config";
import CreatorsWithProgramHttpClient from "@src/creators/CreatorsWithProgramsHttpClient";
import CreatorWithCreatorPrograms from "@src/creators/CreatorWithCreatorPrograms";
import CreatorWithPayableStatus from "@src/creators/CreatorWithPayableStatus";

/**
 * @deprecated
 */
class AuthenticateCreatorAction {
  constructor(
    private readonly provider: AuthenticationProvider,
    private readonly creators: CreatorsHttpClient,
    private readonly redirectUrl: URL,
    private readonly creatorsWithProgramHttpClient: CreatorsWithProgramHttpClient
  ) {
    this.provider = provider;
    this.creators = creators;
    this.creatorsWithProgramHttpClient = creatorsWithProgramHttpClient;
  }

  /**
   * @throws BadCredentials If authorization code cannot be exchanged for an access token
   * @throws UnknownCreator If no creator with the given Nucleus ID can be found
   */
  async execute(input: AuthenticateCreatorInput): Promise<CreatorWithPayableStatus> {
    const token: EAToken = input.token();
    await this.provider.authenticate(token, this.redirectUrl);

    const identity: Identity = token.identity();
    const creators = await this.creators.matching(new CreatorCriteria(identity.nucleusId));
    if (creators.length === 1) {
      const creator = creators[0];
      await this.creators.syncIdentity(creator.syncIdentity(identity));

      return config.FLAG_CREATORS_API_WITH_PROGRAM
        ? await this.creatorsWithProgramHttpClient.withId(creator.id)
        : creator;
    }

    throw UnknownCreator.withIdentity(
      identity.originEmail,
      identity.nucleusId,
      identity.defaultGamerTag,
      identity.dateOfBirth
    );
  }

  async executeCreatorPrograms(input: AuthenticateCreatorInput): Promise<CreatorWithCreatorPrograms> {
    const token: EAToken = input.token();
    await this.provider.authenticate(token, this.redirectUrl);
    const identity: Identity = token.identity();
    const creators = await this.creators.creatorWithPrograms(new CreatorCriteria(identity.nucleusId));
    if (creators.length === 1) {
      const creator = creators[0];
      await this.creators.syncIdentity(creator.syncIdentity(identity));
      return creator;
    }

    throw UnknownCreator.withIdentity(
      identity.originEmail,
      identity.nucleusId,
      identity.defaultGamerTag,
      identity.dateOfBirth
    );
  }
}

export default AuthenticateCreatorAction;
