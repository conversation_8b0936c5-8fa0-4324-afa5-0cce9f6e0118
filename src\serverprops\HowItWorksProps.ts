import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import HowItWorksPagePropsController from "./controllers/HowItWorksPagePropsController";
import config from "config";

const howItWorksProps = (locale: string) =>
  serverPropsControllerFactory(
    new HowItWorksPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default howItWorksProps;
