import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { TrustAndSafetyGuidelinesProps } from "pages/trust-and-safety-guidelines";
import featureFlags from "../../../utils/feature-flags";

export default class TrustAndSafetyGuidelinesPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<TrustAndSafetyGuidelinesProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<TrustAndSafetyGuidelinesProps>> {
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.defaultAvatar, this.program)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled(),
        interestedCreator: featureFlags.isInterestedCreatorFlowEnabled(),
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "trust-and-safety-guidelines",
          "notifications",
          "connect-accounts",
          "opportunities"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled()
      }
    };
  }
}
