import "reflect-metadata";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "../config";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { memo, useEffect, useMemo, useState } from "react";
import labelsCommon from "../config/translations/common";
import labelsHow from "../config/translations/how-it-works";
import withUserSession from "../src/utils/WithUserSession";
import BrowserAnalytics, { AuthenticatedUser, AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import flags from "../utils/feature-flags";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import Footer from "../components/footer/ProgramFooter";
import HowItWorksPage from "../components/pages/how-it-works/HowItWorksPage";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { mapNotificationsBellLabels } from "@config/translations/mappers/notifications";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import { GetServerSidePropsResult } from "next";
import { CreatorType } from "@eait-playerexp-cn/metadata-types";
import howItWorksProps from "@src/serverprops/HowItWorksProps";

export type HowItWorksProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  interestedCreator: boolean;
  creatorTypesFallback: CreatorType[];
  analytics?: BrowserAnalytics;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  FLAG_NEW_FOOTER_ENABLED: boolean;
};

export default memo(function HowItWorks({
  user,
  creatorTypesFallback,
  interestedCreator,
  analytics = new BrowserAnalytics(),
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_NEW_FOOTER_ENABLED
}: HowItWorksProps) {
  const { metadataClient } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const { locale, pathname: page } = useRouter();
  const { t } = useTranslation(["common", "how", "notifications", "connect-accounts", "opportunities"]);
  const [liveStreamUrl, setLiveStreamUrl] = useState(null);
  const { hero, steps, creator, startCreating, layout, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    return {
      ...labelsHow(t),
      layout: labelsCommon(t),
      notificationsLabels: notificationBellLabels
    };
  }, [t]);

  const labels = {
    hero,
    steps,
    creator,
    startCreating
  };

  const [creatorsType, setCreatorsType] = useState(null);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };

  const setHeroImage = (creatorsType) => {
    creatorsType.find((type) => {
      if (type.value === "LIVE_STREAMER") {
        setLiveStreamUrl(type.imageAsIcon);
      }
    });
  };

  useEffect(() => {
    async function getCreatorTypes() {
      try {
        const allCreatorTypes = await metadataService.getCreatorTypes();
        const creatorTypes = allCreatorTypes.filter((item) => item.label !== "other");
        setCreatorsType(creatorTypes);
        setHeroImage(creatorTypes);
        if (user) {
          analytics.viewedMarketingPage({ locale: locale, page });
        }
      } catch {
        setCreatorsType(creatorTypesFallback);
        setHeroImage(creatorTypesFallback);
      }
    }
    getCreatorTypes();
  }, []);

  const howItWorkLabels = { ...labels, layout };

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.works}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={interestedCreator}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
        <HowItWorksPage
          user={user}
          interestedCreator={interestedCreator}
          creatorsType={creatorsType}
          liveStreamUrl={liveStreamUrl}
          labels={howItWorkLabels}
          t={t}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={locale}
          labels={layout.footer}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
});

export const getServerSideProps = async ({ locale, req, res }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(howItWorksProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<HowItWorksProps>;
  }

  const user = await withUserSession(req, res);
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      interestedCreator: flags.isInterestedCreatorFlowEnabled(),
      creatorTypesFallback: config.FALLBACK_CREATOR_TYPES,
      ...(await serverSideTranslations(locale, [
        "common",
        "how",
        "creator-type",
        "notifications",
        "connect-accounts",
        "opportunities"
      ])),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled()
    }
  };
};
