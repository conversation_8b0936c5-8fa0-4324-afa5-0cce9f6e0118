import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import PaymentsHistoryHttpClient from "../paymentsHistories/PaymentsHistoryHttpClient";
import { PaymentsCriteria } from "../api/services/PaymentsService";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ViewPaymentsHistoryController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly paymentsHistory: PaymentsHistoryHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const criteria: PaymentsCriteria = req.query as unknown as PaymentsCriteria;
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);

    const paymentsHistory = await this.paymentsHistory.matching(user.id, criteria);

    this.json(res, paymentsHistory);
  }
}

export default ViewPaymentsHistoryController;
