import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import OpportunityDetailPagePropsController from "./controllers/OpportunityDetailPagePropsController";
import config from "config";

const opportunityDetailProps = (locale: string) =>
  serverPropsControllerFactory(
    new OpportunityDetailPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default opportunityDetailProps;
