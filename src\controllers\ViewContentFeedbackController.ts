import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import ContentFeedbackHttpClient, {
  SubmittedContentFeedbackCriteria
} from "../submittedContent/ContentFeedbackHttpClient";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewContentFeedbackController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions, private readonly contents: ContentFeedbackHttpClient) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const criteria = req.query as unknown as SubmittedContentFeedbackCriteria;

    const submittedContentsFeedback = await this.contents.matching(criteria);

    this.json(res, submittedContentsFeedback);
  }
}

export default ViewContentFeedbackController;
