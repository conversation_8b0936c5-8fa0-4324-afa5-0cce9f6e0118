import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { anInterestedCreatorApplication } from "__tests__/factories/interestedCreators/InterestedCreatorApplication";
import ApplicationPending from "../../../pages/interested-creators/application-pending";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";
import InterestedCreatorApplicationStatus from "@src/interestedCreators/InterestedCreatorApplicationStatus";
import { ApplicationPendingPageLabels } from "@src/contentManagement/ApplicationPendingPageMapper";

jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("InterestedCreatorsApplicationPending", () => {
  const locale = "en-us";
  const mockPush = jest.fn();
  const mockCheckedApplicationStatus = jest.fn();
  const analytics = {
    checkedApplicationStatus: mockCheckedApplicationStatus
  } as unknown as BrowserAnalytics;

  const basePageLabels = {
    applicationPendingLabels: {
      pageTitle: "Application Pending",
      title: "Application Under Review",
      description: "We are reviewing your application",
      returnToCreatorNetwork: "Return to Creator Network",
      gamerTag: "Gamer Tag",
      pending: "Pending",
      submissionUpdateDescription: "Update description",
      submissionUpdate: "Update",
      reviewAndResubmit: "Review & Resubmit",
      submissionReceived: "Submission Received",
      submissionReceivedDescription: "We received your submission",
      unReviewed: "Unreviewed",
      status: "Status",
      email: "Email",
      submissionDate: "Submission Date",
      returnToHomePage: "Return to Home Page",
      subTitle: "Subtitle",
      applicationPendingDescription: "Pending description",
      programLabel: "Program",
      programName: "Creator Network"
    },
    commonPageLabels: {
      creatorNetwork: "Creator Network",
      close: "Close"
    }
  } as unknown as ApplicationPendingPageLabels & CommonPageLabels;

  const testApplication = anInterestedCreatorApplication() as InterestedCreatorApplicationStatus;

  const baseApplicationPendingProps = {
    locale,
    application: testApplication,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    RE_APPLY_THRESHOLD_IN_DAYS: 0,
    pageLabels: basePageLabels,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale,
      push: mockPush
    }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { FLAG_INTERESTED_CREATOR_CAN_APPLY: false }
    });
  });

  it("shows remote application pending component", async () => {
    render(<ApplicationPending {...baseApplicationPendingProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with correct page title configuration", async () => {
    render(<ApplicationPending {...baseApplicationPendingProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with analytics configuration", async () => {
    render(<ApplicationPending {...baseApplicationPendingProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows header with correct labels", async () => {
    render(<ApplicationPending {...baseApplicationPendingProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });

  it("renders with INTERESTED_CREATOR_REAPPLY_PERIOD enabled", async () => {
    const propsWithReapplyEnabled = {
      ...baseApplicationPendingProps,
      INTERESTED_CREATOR_REAPPLY_PERIOD: true
    };

    render(<ApplicationPending {...propsWithReapplyEnabled} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with different RE_APPLY_THRESHOLD_IN_DAYS", async () => {
    const propsWithDifferentThreshold = {
      ...baseApplicationPendingProps,
      RE_APPLY_THRESHOLD_IN_DAYS: 30
    };

    render(<ApplicationPending {...propsWithDifferentThreshold} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("renders with FLAG_INTERESTED_CREATOR_CAN_APPLY enabled", async () => {
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { FLAG_INTERESTED_CREATOR_CAN_APPLY: true }
    });

    render(<ApplicationPending {...baseApplicationPendingProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("passes correct props to ApplicationPendingPage component", async () => {
    render(<ApplicationPending {...baseApplicationPendingProps} />);

    const dynamicComponent = await screen.findByTestId("dynamic");
    
    expect(dynamicComponent).toBeInTheDocument();
    expect(dynamicComponent).toHaveAttribute("data-testid", "dynamic");
  });

  it("renders with different locale", async () => {
    const propsWithDifferentLocale = {
      ...baseApplicationPendingProps,
      locale: "fr-fr"
    };

    render(<ApplicationPending {...propsWithDifferentLocale} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("handles missing page labels gracefully", () => {
    const propsWithMinimalLabels = {
      ...baseApplicationPendingProps,
      pageLabels: {
        applicationPendingLabels: {
          pageTitle: "Test Title"
        },
        commonPageLabels: {
          creatorNetwork: "Network",
          close: "Close"
        }
      } as unknown as ApplicationPendingPageLabels & CommonPageLabels
    };

    expect(() => render(<ApplicationPending {...propsWithMinimalLabels} />)).not.toThrow();
  });

  it("renders with default analytics when not provided", () => {
    const propsWithoutAnalytics = {
      ...baseApplicationPendingProps,
      analytics: undefined
    };

    expect(() => render(<ApplicationPending {...propsWithoutAnalytics} />)).not.toThrow();
  });
});
