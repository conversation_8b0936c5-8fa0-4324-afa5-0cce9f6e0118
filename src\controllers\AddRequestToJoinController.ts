import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import InterestedCreatorsHttpClient from "../interestedCreators/InterestedCreatorsHttpClient";
import InterestedCreator from "../interestedCreators/InterestedCreator";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class AddRequestToJoinController extends Request<PERSON><PERSON><PERSON> implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly interestedCreators: InterestedCreatorsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.interestedCreators.addRequestToJoinFor(req.body as InterestedCreator);
    this.empty(res);
  }
}

export default AddRequestToJoinController;
