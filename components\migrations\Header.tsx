import React, { memo } from "react";
import Link from "next/link";
import { close, Icon, leftArrow, Stepper } from "@eait-playerexp-cn/core-ui-kit";
import classNames from "classnames";
import { useRouter } from "next/router";
import <PERSON>a<PERSON>ogo from "../icons/EaLogo";
import { useDetectScreen } from "../../utils";
import { USER_NAVIGATED } from "../../utils";
import { NavStep } from "@components/MigrationLayout";

type BackButtonProps = {
  back: string;
  isFirstStep: boolean;
  onGoBack: () => void;
};

export const BackButton: React.FC<BackButtonProps> = ({ back, isFirstStep, onGoBack }) => {
  return (
    <>
      {!isFirstStep && (
        <button
          className={classNames({ "display-back-bt": !isFirstStep, "hide-back-bt": isFirstStep })}
          onClick={onGoBack}
        >
          <Icon icon={leftArrow} className="breadcrumb-nav" />
          <span>{back}</span>
        </button>
      )}
    </>
  );
};

export type HeaderProps = {
  onClose: () => void;
  steps: Array<NavStep>;
  currentPage: NavStep | null | object;
  labels: {
    back: string;
    title: string;
    close: string;
  };
  onGoBack: () => void;
  stableDispatch: (action) => void;
  setShowMigration: (value) => void;
  setNavigateToPage: (value) => void;
  completedLabel: string;
};

const Header: React.FC<HeaderProps> = ({
  onClose,
  steps,
  currentPage,
  labels,
  onGoBack,
  stableDispatch,
  setShowMigration,
  setNavigateToPage,
  completedLabel
}) => {
  const router = useRouter();

  const stepPos = steps?.findIndex(({ title }) => title === (currentPage as NavStep)?.title);

  const handleStepperNavigation = (href) => {
    if (stableDispatch) {
      stableDispatch({ type: USER_NAVIGATED, data: true });
    }
    if (setShowMigration) {
      /** Show the confirmation modal even with stepper navigation */
      setShowMigration(true);
      setNavigateToPage(href);
    } else {
      router.push(href);
      if (stableDispatch) {
        stableDispatch({ type: USER_NAVIGATED, data: false });
      }
    }
  };

  const isMobileOrTab = useDetectScreen(1279);

  return (
    <>
      <div className="mg-header-container">
        <div className="mg-header">
          {/** This back button will be visible only in mobile & tablet views */}
          {isMobileOrTab && (
            <div className="mg-header-back">
              <BackButton back={labels.back} isFirstStep={stepPos === 0} onGoBack={onGoBack} />
            </div>
          )}
          <div className="mg-header-logo">
            <Link href="/">
              <Icon icon={EaLogo} width="3rem" height="3rem" />
              <span>{labels.title}</span>
            </Link>
          </div>
          <div className="mg-header-close">
            <button onClick={onClose} aria-label={labels.close}>
              <Icon icon={close} />
            </button>
          </div>
        </div>
        <div className="stepper-back">
          <BackButton back={labels.back} isFirstStep={stepPos === 0} onGoBack={onGoBack} />
        </div>
        <Stepper
          {...{
            steps,
            current: (currentPage as NavStep)?.title,
            handleNavigate: handleStepperNavigation,
            completedLabel: completedLabel
          }}
        />
      </div>
    </>
  );
};

BackButton.defaultProps = {
  onGoBack: () => {}
};

export default memo(Header);
