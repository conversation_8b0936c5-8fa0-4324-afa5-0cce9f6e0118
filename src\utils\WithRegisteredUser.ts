import User from "../authentication/User";
import withLocalizedUrl from "./WithLocalizedUrl";
import RedirectException from "./RedirectException";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";

/**
 * @deprecated
 */
export default function withRegisteredUser(req: NextApiRequestWithSession, locale: string, user: User | null): void {
  if (!user) return;

  if (User.from(user).isUnregistered()) {
    throw new RedirectException({
      redirect: { destination: encodeURI(`${withLocalizedUrl(req, locale)}onboarding/information`), statusCode: 302 }
    });
  }
}
