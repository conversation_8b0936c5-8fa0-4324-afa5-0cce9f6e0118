import { Inject, Service } from "typedi";
import type ValidRegistrationCodes from "../../validRegistrationCodes/ValidRegistrationsCodes";
import FindValidRegistrationCodeInput from "./FindValidRegistrationCodeInput";

/**
 * @deprecated
 */
@Service()
class FindValidRegistrationCodeAction {
  constructor(
    @Inject("registrations")
    private codes: ValidRegistrationCodes
  ) {}

  async execute(input: FindValidRegistrationCodeInput): Promise<void> {
    await this.codes.matching(input.criteria);
  }
}

export default FindValidRegistrationCodeAction;
