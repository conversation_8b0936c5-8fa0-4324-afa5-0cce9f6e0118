import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import CreatorCriteria from "./CreatorCriteria";
import CreatorWithFlaggedStatus from "./CreatorWithFlaggedStatus";
import CreatorsHttpClient from "./CreatorsHttpClient";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class SearchCreatorsWithFlaggedStatusHttpClient extends CreatorsHttpClient {
  constructor(@Inject("operationsClient") client: TraceableHttpClient) {
    super(client);
  }

  async matching(criteria: CreatorCriteria): Promise<Array<CreatorWithFlaggedStatus>> {
    const response = (await this.client.get("/v3/creators", { query: criteria })) as AxiosResponse;
    return response.data
      .filter((c) => c.accountInformation.nucleusId === criteria.nucleusId)
      .map((data) => CreatorWithFlaggedStatus.fromApi(data));
  }
}

export default SearchCreatorsWithFlaggedStatusHttpClient;
