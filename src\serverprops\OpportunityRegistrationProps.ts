import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import OpportunityRegistrationPagePropsController from "./controllers/OpportunityRegistrationPagePropsController";
import config from "config";

const opportunityRegistrationProps = (locale: string) =>
  serverPropsControllerFactory(
    new OpportunityRegistrationPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default opportunityRegistrationProps;
