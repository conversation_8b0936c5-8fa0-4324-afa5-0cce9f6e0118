import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import FranchisesYouPlayPagePropsController from "./controllers/FranchisesYouPlayPagePropsController";
import config from "config";

const franchisesYouPlayProps = (locale: string) =>
  serverPropsControllerFactory(
    new FranchisesYouPlayPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default franchisesYouPlayProps;
