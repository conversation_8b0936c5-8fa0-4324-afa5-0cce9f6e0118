import "reflect-metadata";
import React, { ComponentType, FC, useMemo } from "react";
import { useTranslation } from "react-i18next";
import labelsC<PERSON>mon from "../config/translations/common";
import withAuthenticatedUser from "../src/utils/WithAuthenticatedUser";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useRouter } from "next/router";
import RedirectException from "../src/utils/RedirectException";
import BrowserAnalytics, { AuthenticatedUser, AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import ContentManagementService from "../src/api/services/ContentManagementService";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Header from "../components/header/Header";
import User from "@src/authentication/User";
import Loading from "@components/Loading";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import Footer from "../components/footer/ProgramFooter";
import flags from "../utils/feature-flags";
import ApiContainer from "@src/ApiContainer";
import withRegisteredUser from "../src/utils/WithRegisteredUser";
import withTermsAndConditionsUpToDate from "../src/utils/WithTermsAndConditionsUpToDate";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import notificationsProps from "@src/serverprops/NotificationsProps";

export type NotificationsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  pageLabels: {
    notificationsPageLabels: Record<string, unknown>;
    notificationsBellLabels: Record<string, unknown>;
  };
  analytics?: BrowserAnalytics;
  notificationsClient?: {
    baseUrl: string;
    timeoutInMilliseconds: number;
    serviceName: string;
  };
  FLAG_NEW_NAVIGATION_ENABLED?: boolean;
  FLAG_NEW_FOOTER_ENABLED?: boolean;
};

const NotificationCenterPage: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("notifications/NotificationCenterPage"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

const Notifications: FC<NotificationsProps> = ({
  user,
  pageLabels,
  analytics = new BrowserAnalytics(),
  FLAG_NEW_NAVIGATION_ENABLED,
  FLAG_NEW_FOOTER_ENABLED
}) => {
  const router = useRouter();
  const {
    notificationsClient,
    configuration: { NOTIFICATION_BASE_URLS, SINGLE_PROGRAM_NOTIFICATIONS, PROGRAM_CODE, DEFAULT_NOTIFICATION_PROGRAM }
  } = useDependency();
  const { notificationsBellLabels, notificationsPageLabels } = pageLabels;
  const { t } = useTranslation(["common", "notifications", "connect-accounts", "opportunities"]);
  const { layout } = useMemo(() => {
    const common = labelsCommon(t);

    return {
      layout: {
        ...common,
        footer: { locale: router.locale, labels: common.footer },
        pageTitle: common.header.notifications,
        user: user
      }
    };
  }, [router, t]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const footerLabels = layout.footer.labels;

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.notifications}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsBellLabels}
          analytics={analytics}
          interestedCreator={null}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody
        showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}
        className={
          !FLAG_NEW_NAVIGATION_ENABLED ? "notification-container notification-spacing" : "notification-container"
        }
      >
        <NotificationCenterPage
          labels={notificationsPageLabels}
          configuration={{
            client: notificationsClient,
            programHosts: NOTIFICATION_BASE_URLS,
            program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
            defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
          }}
          locale={router.locale}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={router.locale}
          labels={footerLabels}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
};

export default Notifications;

type NotificationsPage = {
  user: AuthenticatedUser;
};

export const getServerSideProps: GetServerSideProps<NotificationsPage> = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();

    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .use(saveInitialPage(locale))
      .use(verifyAccessToProgram)
      .use(addLocaleCookie(locale))
      .use(checkTermsAndConditionsOutdated(locale))
      .get(notificationsProps(locale));

    return (await router.run(req, res)) as GetServerSidePropsResult<NotificationsProps>;
  }

  let user: User;
  try {
    user = await withAuthenticatedUser(req, res, locale);
    withRegisteredUser(req, locale, user);
    await withTermsAndConditionsUpToDate(req, locale, user);
  } catch (e) {
    if (e instanceof RedirectException) return e.redirect;
    throw e;
  }

  const pageLabels = await ApiContainer.get(ContentManagementService).getPageLabels(locale, "notifications");
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      ...(await serverSideTranslations(locale, ["common", "notifications", "connect-accounts", "opportunities"])),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled(),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      pageLabels
    }
  };
};
