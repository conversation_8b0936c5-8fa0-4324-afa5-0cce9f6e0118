import React, { createContext, Dispatch, ReactNode, useContext } from "react";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Action } from "@src/errorHandling/errorHandler";

const DependencyContext = createContext(undefined);

export function useDependency() {
  return useContext(DependencyContext);
}

export function DependencyProvider({
  configuration,
  errorHandler,
  metadataClient,
  notificationsClient,
  creatorsClient,
  children
}: {
  configuration: Record<string, unknown>;
  errorHandler: (dispatch: Dispatch<Action>, e: Error) => void;
  metadataClient: TraceableHttpClient;
  notificationsClient: TraceableHttpClient;
  creatorsClient: TraceableHttpClient;
  children: ReactNode;
}) {
  return (
    <DependencyContext.Provider
      value={{ configuration, errorHandler, metadataClient, notificationsClient, creatorsClient }}
    >
      {children}
    </DependencyContext.Provider>
  );
}
