import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import AgeRestrictionPagePropsController from "./controllers/AgeRestrictionPagePropsController";
import ContentManagementService from "@src/api/services/ContentManagementService";

const ageRestrictionProps = (locale: string) =>
  serverPropsControllerFactory(
    new AgeRestrictionPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale
    )
  );

export default ageRestrictionProps;
