import { HttpStatus } from "@eait-playerexp-cn/http";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { HomeProps } from "pages";
import featureFlags from "utils/feature-flags";

export default class HomePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<HomeProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<HomeProps>> {
    if (this.hasIdentity(req) && this.identity(req).hasAccessTo(this.program)) {
      return {
        redirect: {
          destination: `${this.localePathSegment(req, this.currentLocale)}dashboard`,
          statusCode: HttpStatus.FOUND_CODE
        }
      };
    }
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.defaultAvatar, this.program)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        interestedCreator: featureFlags.isInterestedCreatorFlowEnabled(),
        user: authenticatedUser,
        creatorTypesFallback: config.FALLBACK_CREATOR_TYPES,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "index",
          "creator-type",
          "opportunities-rewards",
          "notifications",
          "connect-accounts",
          "opportunities"
        ])),
        FLAG_NEW_FOOTER_ENABLED: featureFlags.isNewFooterEnabled()
      }
    };
  }
}
