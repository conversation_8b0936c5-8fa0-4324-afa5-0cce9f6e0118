{"filters": "Filtres", "dateRange": "Zeitraum", "startDate": "Startdatum", "endDate": "Enddatum", "paymentStatus": "Zahlungsstatus", "opportunityType": "<PERSON><PERSON>", "applyFilters": "Übernehmen", "startDateError": "Startdatum muss früher als das Enddatum sein", "endDateError": "Enddatum muss später als das Startdatum sein", "startDateRequired": "Startdatum ist erforderlich", "endDateRequired": "Enddatum ist erforderlich", "range": {"allTime": "Gesamte Zeit", "thisMonth": "<PERSON><PERSON>", "past30Days": "Vergangene 30 Tage", "past90Days": "Vergangene 90 Tage", "past6Months": "Vergangene 6 Monate", "yearToDate": "Jahr bis zum aktuellen Datum", "lastYear": "Letztes Jahr", "custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"all": "Alle", "processed": "Bearbeitet", "pending": "<PERSON><PERSON><PERSON><PERSON>"}, "type": {"all": "Alle", "opportunity": "Opportunity", "creatorCode": "Creator-Code", "simsMakerProgram": "<PERSON>-Programm"}}