import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import HomePagePropsController from "./controllers/HomePagePropsController";
import config from "config";

const homeProps = (locale: string) =>
  serverPropsControllerFactory(
    new HomePagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default homeProps;
