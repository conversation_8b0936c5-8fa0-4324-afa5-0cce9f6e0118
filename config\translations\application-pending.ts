export default function labelsApplicationPending(t, thresholdInDays) {
  return {
    title: t("application-pending:title"),
    description: t("application-pending:description"),
    returnToCreatorNetwork: t("application-pending:returnToCreatorNetwork"),
    pending: t("application-pending:pending"),
    unReviewed: t("application-pending:unReviewed"),
    email: t("application-pending:email"),
    gamerTag: t("application-pending:gamerTag"),
    status: t("application-pending:status"),
    submissionReceived: t("application-pending:submissionReceived"),
    submissionReceivedDescription: t("application-pending:submissionReceivedDescription", { thresholdInDays }),
    submissionDate: t("application-pending:submissionDate"),
    submissionUpdate: t("application-pending:submissionUpdate"),
    submissionUpdateDescription: t("application-pending:submissionUpdateDescription"),
    reviewAndResubmit: t("application-pending:reviewAndResubmit")
  };
}
