import "reflect-metadata";
import { NextApiResponse } from "next";
import config from "../../config";
import ApiContainer from "../ApiContainer";
import { NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";
import CachedTermsAndConditions from "@src/pactSafe/CachedTermsAndConditions";
import User from "@src/authentication/User";
import { DEFAULT_LOCALE } from "../../utils";

/**
 * @deprecated
 */
export default async function withNeedToSignTC(
  req: NextApiRequestWithSession,
  res: NextApiResponse,
  locale: string
): Promise<User | null> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());
  let user: User = (req.session.user as User) || null;
  const urlLocale = `${(locale !== DEFAULT_LOCALE && "/" + locale) || ""}`;

  if (user) {
    user = User.from(user);
    const termsAndConditions: CachedTermsAndConditions = ApiContainer.get(CachedTermsAndConditions);
    const isStatusUpToDate = await termsAndConditions.signedStatusWithProgram(user.id, locale, config.PROGRAM_CODE);
    if (user.isActive() && isStatusUpToDate.upToDate) {
      res.setHeader("Location", encodeURI(`${urlLocale}/dashboard`));
      res.statusCode = 302;
      res.end();
    }
  }
  return Promise.resolve(user);
}
