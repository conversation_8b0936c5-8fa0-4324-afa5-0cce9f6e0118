import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { AxiosResponse } from "axios";

export type MicroCopiesCriteria = {
  locale: string;
  titles: string[];
  programCode: string;
};
export type MicroCopies = {
  data:
    | Record<string, string>
    | {
        error?: string;
        error_description?: string;
        code?: string;
        message?: string;
        status?: number;
        errors?: Record<string, string>;
      };
};

@Service()
export default class MicroCopiesHttpClient {
  constructor(@Inject("contentManagementClient") private client: TraceableHttpClient) {}

  /**
   *  @see {@link https://content-management-api-eait-playerexp-cn-cn-serv-69e7ace1b977fe.gitlab.ea.com/docs/api.html#tag/MicroCopies/operation/viewMicroCopies View Micro Copies}
   */
  async matchingCriteria(criteria: MicroCopiesCriteria): Promise<MicroCopies> {
    const microCopies = (await this.client.get("/v1/micro-copies", {
      query: criteria
    })) as AxiosResponse<MicroCopies>;
    return microCopies.data;
  }
}
