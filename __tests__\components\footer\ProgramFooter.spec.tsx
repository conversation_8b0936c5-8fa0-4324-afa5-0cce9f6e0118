import { act, render, screen, waitFor } from "@testing-library/react";
import { commonTranslations } from "../../translations";
import { useRouter } from "next/router";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import ProgramFooter from "@components/footer/ProgramFooter";
import { mockMatchMedia } from "../../helpers/window";
import { useDetectScreen } from "../../../utils";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../utils");
jest.mock("../../../src/context/DependencyContext");

describe("ProgramFooter", () => {
  mockMatchMedia();
  const { footer } = commonTranslations;
  const footerLinks = [
    ["How it Works", "/how-it-works"],
    ["FAQs", "/faq"],
    ["Policy", "/trust-and-safety-guidelines"],
    ["Disclosure", "/disclosure"],
    ["Perks & Rewards", "/opportunities-rewards"]
  ];
  const analytics = { clickedFooterLink: jest.fn() } as unknown as BrowserAnalytics;
  const mockUseDetectScreen = useDetectScreen as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { SUPPORTED_LOCALES: ["en-us"], PROGRAM_CODE: "creator_network" }
    });
  });

  it("shows 'disclaimer' Link", () => {
    render(<ProgramFooter labels={footer} locale={""} analytics={analytics} FLAG_NEW_FOOTER_ENABLED />);

    expect(screen.getByRole("link", { name: /disclaimer/i })).toBeInTheDocument();
  });

  it.each(footerLinks)("logs 'Link Clicked' event when clicking on '%s' marketing page", async (name, url) => {
    mockUseDetectScreen.mockImplementation((width) => width === 10000);
    const locale = "en-us";
    const push = jest.fn();
    (useRouter as jest.Mock).mockImplementation(() => ({ push, locale }));
    render(<ProgramFooter labels={footer} locale={"en-us"} analytics={analytics} FLAG_NEW_FOOTER_ENABLED />);

    await userEvent.click(screen.getByRole("button", { name }));

    await waitFor(async () => {
      expect(analytics.clickedFooterLink).toHaveBeenCalledWith({ locale, url });
      expect(analytics.clickedFooterLink).toHaveBeenCalledTimes(1);
      expect(push).toHaveBeenCalledWith(url);
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(
      <ProgramFooter labels={footer} locale={""} analytics={analytics} FLAG_NEW_FOOTER_ENABLED />
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
