import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import OpportunityPagePropsController from "./controllers/OpportunityPagePropsController";
import config from "config";

const opportunityProps = (locale: string) =>
  serverPropsControllerFactory(
    new OpportunityPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default opportunityProps;
