import config from "../config";

function isInterestedCreatorFlowEnabled(): boolean {
  return config.INTERESTED_CREATOR;
}

function isUpdateOpportunityDetailsEnabled(): boolean {
  return config.UPDATE_OPPORTUNITY_DETAILS;
}

function isInterestedCreatorReApplyEnabled(): boolean {
  return config.INTERESTED_CREATOR_REAPPLY_PERIOD;
}

function isInstagramMediaSupportEnabled(): boolean {
  return config.FLAG_INSTAGRAM_MEDIA_SUPPORT_ENABLED;
}

function isInitialMessageEnabled(): boolean {
  return config.FLAG_INITIAL_MESSAGE;
}

function isCountriesByTypeEnabled(): boolean {
  return config.FLAG_COUNTRIES_BY_TYPE;
}

function isNewNavigationEnabled(): boolean {
  return config.FLAG_NEW_NAVIGATION_ENABLED;
}

function isNewFooterEnabled(): boolean {
  return config.FLAG_NEW_FOOTER_ENABLED;
}

function isOnboardingCustomLinksEnabled(): boolean {
  return config.FLAG_ONBOARDING_CUSTOM_LINKS;
}

function isCreatorsAPIWithProgram(): boolean {
  return config.FLAG_CREATORS_API_WITH_PROGRAM;
}

function isContentWithFinalRemarksEnabled(): boolean {
  return config.FLAG_CONTENT_WITH_FINAL_REMARK;
}

function isPerProgramProfileEnabled(): boolean {
  return config.FLAG_PER_PROGRAM_PROFILE;
}

const featureFlags = {
  isInterestedCreatorFlowEnabled,
  isUpdateOpportunityDetailsEnabled,
  isInterestedCreatorReApplyEnabled,
  isInstagramMediaSupportEnabled,
  isInitialMessageEnabled,
  isCountriesByTypeEnabled,
  isNewNavigationEnabled,
  isNewFooterEnabled,
  isOnboardingCustomLinksEnabled,
  isCreatorsAPIWithProgram,
  isContentWithFinalRemarksEnabled,
  isPerProgramProfileEnabled
};

export default featureFlags;
