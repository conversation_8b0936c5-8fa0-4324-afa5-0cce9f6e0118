import Notifications from "./Notifications";
import FutureCreator from "./FutureCreator";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class NotificationsHttpClient implements Notifications {
  constructor(@Inject("communicationsClient") private client: TraceableHttpClient) {}

  async notify(futureCreator: FutureCreator): Promise<void> {
    await this.client.post(`/v1/notify-me/`, { body: futureCreator });
  }
}

export default NotificationsHttpClient;
