import SubmittedContentPage from "./SubmittedContentPage";
import OpportunitySubmittedContent from "./OpportunitySubmittedContent";
import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type WebsiteContent = {
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  contentType: string;
};

export type Contents = {
  id: string;
  name: string;
  type: string;
  thumbnail: string;
  status: string;
  submittedOn: number;
  contentUri: string;
  contentType: string;
  submittedDate: number;
  opportunityName: string;
  opportunityId: string;
  sourceType: string;
};

export type UpdateWebsiteContent = {
  id: string;
  contentDetails: WebsiteContent;
};

export type SocialMediaContent = {
  creatorId: string;
  contentUrl: string;
};

export type SocialMediaTikTokContent = SocialMediaContent & {
  accountId: string;
};

export type SocialMediaContentDeliverable = SocialMediaTikTokContent & {
  deliverableId: string;
};

export type UploadedFileContent = {
  fileName: string;
  title: string;
  versionId: string;
  thumbnail: string;
  id: string;
  contentType: string;
};

export type WebsiteContentDeliverable = WebsiteContent & {
  deliverableId: string;
};

export type UploadedFileContentDeliverable = UploadedFileContent & {
  deliverableId: string;
};

export type SubmittedContentCriteria = {
  creatorId: string;
  participationId: string;
  page: number;
  limit: number;
  deliverableId: string;
};

export type SubmittedContentCriteriaWithProgramCode = SubmittedContentCriteria & {
  program: string;
};

export type ContentsWithDeliverable = Contents & { deliverableId: string };

export type SubmittedContentWithDeliverablePage = {
  count: number;
  total: number;
  contents: ContentsWithDeliverable[];
};

export type ContentsWithReviewFinalRemark = ContentsWithDeliverable & {
  reviewFinalRemark: {
    content: string;
    author: string;
    date: number;
  };
};

export type SubmittedContentWithFinalRemarkPage = {
  count: number;
  total: number;
  contents: ContentsWithReviewFinalRemark[];
};

@Service()
class SubmittedContentHttpClient {
  constructor(@Inject("contentSubmissionClient") private client: TraceableHttpClient) {}

  /** @deprecated */
  async withId(
    creatorId: string | null,
    participationId: string | null,
    pageSize: number,
    pageNumber: number
  ): Promise<SubmittedContentPage> {
    const response = (await this.client.get("/v1/submitted-content", {
      query: {
        creatorId: creatorId,
        participationId: participationId,
        limit: pageSize,
        page: pageNumber
      }
    })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/submitUploadedContent Submit website content}
   */
  async saveWebsiteContent(
    participationId: string,
    websiteContent: WebsiteContent
  ): Promise<Array<OpportunitySubmittedContent>> {
    const response = (await this.client.post(`/v1/participations/${participationId}/website-contents`, {
      body: websiteContent
    })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/updateWebsiteContent Update website content}
   */
  async updateWebsiteContent(
    participationId: string,
    websiteContent: UpdateWebsiteContent
  ): Promise<OpportunitySubmittedContent> {
    const response = (await this.client.put(`/v1/participations/${participationId}/website-contents`, {
      body: websiteContent
    })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#operation/submitUploadedContent Submit uploaded content}
   */
  async saveUploadedContent(participationId: string, uploadedContent: UploadedFileContent): Promise<void> {
    await this.client.post(`/v1/participations/${participationId}/file-contents`, { body: uploadedContent });
  }

  async updateUploadedContent(participationId: string, uploadedContent: UploadedFileContent): Promise<void> {
    await this.client.put(`/v1/participations/${participationId}/file-contents`, { body: uploadedContent });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/saveSocialMediaContentsWithInstagramReelsSupport}
   */
  async saveSocialMediaContents(
    participationId: string,
    socialMediaContentDeliverable: SocialMediaContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v4/participations/${participationId}/social-media-contents`, {
      body: socialMediaContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/saveSocialMediaContentsWithInstagramMultipleMediaSupport}
   */
  async saveSocialMediaContentWithInstagramMedia(
    participationId: string,
    socialMediaContentDeliverable: SocialMediaContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v5/participations/${participationId}/social-media-contents`, {
      body: socialMediaContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/submitWebsiteContentWithDeliverableId Submit website content deliverable}
   */
  async saveWebsiteContentDeliverable(
    participationId: string,
    websiteContentDeliverable: WebsiteContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v2/participations/${participationId}/website-contents`, {
      body: websiteContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/submitUploadedContentWithDeliverableId Submit uploaded content}
   */
  async submitUploadedContentDeliverable(
    participationId: string,
    uploadedFileContentDeliverable: UploadedFileContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v2/participations/${participationId}/file-contents`, {
      body: uploadedFileContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/viewSubmittedContentWithDeliverables}
   **/
  async getSubmittedContents(criteria: SubmittedContentCriteria): Promise<SubmittedContentWithDeliverablePage> {
    const submittedContent = (await this.client.get(`/v4/submitted-content`, { query: criteria })) as AxiosResponse;
    return Promise.resolve(submittedContent.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/viewSubmittedContentWithReviewComments}
   **/
  async getSubmittedContentsFinalRemarks(
    criteria: SubmittedContentCriteria
  ): Promise<SubmittedContentWithFinalRemarkPage> {
    const submittedContent = (await this.client.get(`/v5/submitted-content`, { query: criteria })) as AxiosResponse;
    return Promise.resolve(submittedContent.data);
  }

  /**
   * @see {@link https://dev-services.cn.ea.com/cn-content-submission-api/swagger-ui/index.html?configUrl=/cn-content-submission-api/v3/api-docs/swagger-config#/view-submitted-content-controller/viewSubmittedContentWithProgramCode}
   **/
  async getSubmittedContentsWithProgramCode(
    criteria: SubmittedContentCriteriaWithProgramCode
  ): Promise<SubmittedContentWithFinalRemarkPage> {
    const submittedContent = await this.client.get(`/v6/submitted-content`, { query: criteria });
    return Promise.resolve(submittedContent.data);
  }
}

export default SubmittedContentHttpClient;
