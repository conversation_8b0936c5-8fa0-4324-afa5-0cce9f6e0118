import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import config from "config";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import featureFlags from "utils/feature-flags";

type OpportunityRegistrationProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  FLAG_NEW_NAVIGATION_ENABLED: boolean;
  WATERMARKS_URL: string;
};

export default class OpportunityRegistrationPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<OpportunityRegistrationProps>
{
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession): Promise<GetServerSidePropsResult<OpportunityRegistrationProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.defaultAvatar,
      this.program
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "opportunities",
          "criteria-opportunity",
          "game-code",
          "thanks-opportunity",
          "content-submission"
        ])),
        FLAG_NEW_NAVIGATION_ENABLED: featureFlags.isNewNavigationEnabled(),
        WATERMARKS_URL: config.WATERMARKS_URL
      }
    };
  }
}
