.profile-information {
  @apply flex w-full flex-col items-center justify-center pb-[130px] xl:min-w-[790px];
}
.profile-information form {
  @apply w-full;
}
.personal-information,
.mailing-information,
.legal-entity-information,
.miscellaneous-information {
  @apply mb-[29px] grid grid-cols-1 gap-y-[10px]  border-b border-white border-opacity-[0.33] pb-[25px] md:mb-[43px] md:grid-cols-3 md:gap-x-[2.125rem];
}
.profile-legal-entity-description {
  @apply col-span-3 pb-meas18 font-text-regular text-gray-10 xs:text-mobile-body-default md:w-[640px] md:text-tablet-body-default lg:text-desktop-body-default;
}
.personal-information-sub-tile,
.mailing-information-sub-tile,
.legal-entity-information-sub-tile,
.miscellaneous-information-sub-tile {
  @apply col-span-3 pb-meas4 font-display-regular font-bold text-gray-10 xs:text-mobile-h5 md:col-span-2 md:text-tablet-h5 lg:text-desktop-h5;
}
.personal-field-title,
.mailing-field-title,
.legal-entity-field-title,
.miscellaneous-field-title {
  @apply flex items-center font-text-bold text-gray-10 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.legal-entity-field-title.inline {
  @apply items-start;
}
.legal-entity-type-title {
  @apply items-start;
}
.personal-field,
.mailing-field,
.legal-entity-field,
.miscellaneous-field {
  @apply col-span-3 flex min-h-[2.5rem] items-center whitespace-nowrap break-all font-text-regular text-gray-10 xs:text-mobile-body-default md:col-span-2 md:text-tablet-body-default lg:text-desktop-body-default;
}
.legal-entity-field .radio-btn-container {
  @apply mb-meas4;
}
.form-radio-field {
  @apply flex flex-col;
}
.personal-field .input-box,
.personal-field > label,
.mailing-field .input-box,
.mailing-field > label,
.legal-entity-field .input-box,
.legal-entity-field > label,
.legal-entity-field .input-help-text,
.miscellaneous-field > .input-box,
.personal-field > .form-input-box,
.mailing-field > .form-input-box,
.legal-entity-field > .form-input-box,
.miscellaneous-field > .form-input-box,
.personal-field > .from-text-field,
.legal-entity-field > .from-text-field,
.miscellaneous-field > .from-text-field,
.personal-field > .select-box,
.mailing-field > .select-box,
.legal-entity-field > .select-box,
.miscellaneous-field > .select-box,
.personal-field > .select-header,
.mailing-field > .select-header,
.legal-entity-field > .select-header,
.miscellaneous-field > .select-header {
  @apply w-full md:w-[274px];
}
.miscellaneous-field .select-header-title,
.select-header-label {
  @apply w-full;
}
.miscellaneous-field .select-list,
.select-scroll-list,
.mailing-field .select-list,
.select-scroll-list,
.legal-entity-field .select-list,
.select-scroll-list {
  @apply w-[99.5%];
}
.mailing-field .select-header-title,
.select-header-label {
  @apply w-full;
}
.legal-entity-field .select-header-title,
.select-header-label {
  @apply w-full;
}
.miscellaneous-field > .multi-select-menu {
  @apply w-full md:w-[274px];
}

.form-action > button .icon-block .icon {
  @apply h-meas9 w-meas9;
}
.btn-discord > .icon-block .icon {
  @apply ml-meas0;
}

.legal-entity-field > .checkbox-container {
  @apply ml-meas0;
}

.personal-information .personal-field:nth-last-child(3) {
  @apply md:items-start;
}

.hardware-partner-field {
  @apply overflow-auto;
}

@media screen and (min-width: 959px) and (max-width: 1278px) {
  .personal-information .personal-field:nth-last-child(3) {
    @apply md:items-center;
  }
}
