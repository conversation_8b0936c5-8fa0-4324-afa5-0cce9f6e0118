import { NextApiResponse } from "next";
import User from "../authentication/User";
import saveLocaleCookie from "./SaveLocaleCookie";
import withLocalizedUrl from "./WithLocalizedUrl";
import RedirectException from "./RedirectException";
import { NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";

/**
 * @deprecated
 */
export default async function withAuthenticatedUser(
  req: NextApiRequestWithSession,
  res: NextApiResponse,
  locale?: string
): Promise<User> {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => Promise.resolve());
  saveLocaleCookie(req, res, locale);
  const user = (req.session.user as User) || null;
  if (user) {
    return Promise.resolve(user);
  }

  const urlLocale = withLocalizedUrl(req, locale);
  let pageUrl = req.url;
  // Keeps only the filename without the extension .json
  if (req.url.includes(".json")) {
    pageUrl = `${pageUrl.split(locale)[1]?.split(".")[0]}`;
  }
  if (pageUrl && req.method == "GET") {
    req.session.initialPage = `${urlLocale}${pageUrl}`.replace(/\/{2,}/, "/");
    req.session.save();
  }

  throw new RedirectException({
    redirect: {
      destination: urlLocale,
      statusCode: 302
    }
  });
}
