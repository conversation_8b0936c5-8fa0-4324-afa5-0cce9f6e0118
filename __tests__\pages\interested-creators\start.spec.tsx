import React from "react";
import { useRouter } from "next/router";
import { render, screen, waitFor } from "@testing-library/react";
import ApplicationStart from "../../../pages/interested-creators/start";
import { useDependency } from "@src/context/DependencyContext";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { InformationPageLabels } from "@src/contentManagement/InformationPageMapper";
import { ApplicationStartPageLabels } from "@src/contentManagement/ApplicationStartPageMapper";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

jest.mock("../../../src/context/DependencyContext");
jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("Interested Creator Start Page", () => {
  const mockPush = jest.fn();
  const router = { locale: "en-us", push: mockPush };
  const mockStartedCreatorApplication = jest.fn();
  const analytics = {
    checkedApplicationStatus: jest.fn(),
    startedCreatorApplication: mockStartedCreatorApplication
  } as unknown as BrowserAnalytics;

  const baseApplicationStartProps = {
    pageLabels: {
      applicationStartPageLabels: {
        title: "Start Your Application",
        subTitle: "Join the Creator Network",
        description: "Apply to become a creator",
        descriptionSuffix: "and start earning",
        alreadyApplied: "Already applied?",
        alreadyAppliedSuffix: "Check your status",
        explore: "Explore",
        exploreLeftTitle: "How it works",
        exploreRightTitle: "Opportunities",
        perks: "View perks"
      },
      informationLabels: {
        interestedCreatorTitle: "Start Page"
      },
      commonPageLabels: {
        applyNow: "Apply Now",
        creatorNetwork: "Creator Network",
        close: "Close",
        how: "How it works",
        unhandledError: "An error occurred"
      }
    } as ApplicationStartPageLabels & CommonPageLabels & InformationPageLabels,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        APPLICATIONS_MFE_BASE_URL: "http://localhost:3003",
        FLAG_PER_PROGRAM_PROFILE: false
      }
    });
  });

  it("shows tab title as 'Start your submission'", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    await waitFor(() => {
      expect(document.title).toMatch(/Start Page/);
    });
  });

  it("shows remote Information component", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with analytics configuration", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with applications URL configuration", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with per-program profile configuration", async () => {
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        APPLICATIONS_MFE_BASE_URL: "http://localhost:3003",
        FLAG_PER_PROGRAM_PROFILE: true
      }
    });

    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with close functionality", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows explore pages configuration", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with default analytics when not provided", () => {
    const propsWithoutAnalytics = {
      ...baseApplicationStartProps,
      analytics: undefined
    };

    expect(() => render(<ApplicationStart {...propsWithoutAnalytics} />)).not.toThrow();
  });

  it("shows component with all required labels", async () => {
    render(<ApplicationStart {...baseApplicationStartProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });
});
