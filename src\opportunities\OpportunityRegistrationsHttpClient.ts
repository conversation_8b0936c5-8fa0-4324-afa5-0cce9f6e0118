import { Inject, Service } from "typedi";
import OpportunityRegistrations from "./OpportunityRegistrations";
import JoinOpportunityInput from "../actions/JoinOpportunity/JoinOpportunityInput";
import Participation from "./Participation";
import { AxiosResponse } from "axios";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { opportunitiesApiClient } from "./OpportunitiesHttpClient";

@Service()
export default class OpportunityRegistrationsHttpClient implements OpportunityRegistrations {
  constructor(@Inject(opportunitiesApiClient()) private client: TraceableHttpClient) {}

  async saveParticipation(input: JoinOpportunityInput): Promise<Participation> {
    const response = (await this.client.post(`/v1/opportunities/${input.id}/participations`, {
      body: input.criteria()
    })) as AxiosResponse;
    return Participation.fromApi(response.data);
  }
}
