import { Inject, Service } from "typedi";
import TikTokClient from "../channels/tiktok/TikTokClient";
import { NextApiRequest, NextApiResponse } from "next";
import { Controller, RequestHandler, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";

@Service()
class TikTokLoginController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions, private readonly client: TikTokClient) {
    super(options);
  }

  async handle(_req: NextApiRequest, res: NextApiResponse): Promise<void> {
    this.redirectTo(res, this.client.authorizationUrl());
  }
}

export default TikTokLoginController;
