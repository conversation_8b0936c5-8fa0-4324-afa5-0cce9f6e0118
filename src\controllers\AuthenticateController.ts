import FindValidRegistrationCodeAction from "../actions/ValidRegistrationCodes/FindValidRegistrationCodeAction";
import AuthenticateCreatorInput from "../actions/AuthenticateCreatorInput";
import { NextApiResponse } from "next";
import FutureCreator from "../notifications/FutureCreator";
import User from "../authentication/User";
import CachedTermsAndConditions from "../pactSafe/CachedTermsAndConditions";
import UnknownCreator from "../creators/UnknownCreator";
import FindValidRegistrationCodeInput from "../actions/ValidRegistrationCodes/FindValidRegistrationCodeInput";
import { Inject, Service } from "typedi";
import InterestedCreatorApplicationsHttpClient from "../interestedCreators/InterestedCreatorApplicationsHttpClient";
import InterestedCreator from "../interestedCreators/InterestedCreator";
import InterestedCreatorApplication from "../interestedCreators/InterestedCreatorApplication";
import ServerAnalytics from "../analytics/ServerAnalytics";
import config from "../../config";
import { InitialInterestedCreator } from "../analytics/BrowserAnalytics";
import { InterestedCreatorFactory } from "../interestedCreators/InterestedCreatorFactory";
import CreatorWithFlaggedStatus from "../creators/CreatorWithFlaggedStatus";
import AuthenticateCreatorWithFlaggedStatusAction from "@src/actions/AuthenticateCreatorWithFlaggedStatusAction";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import CreatorWithCreatorPrograms from "@src/creators/CreatorWithCreatorPrograms";

/**
 * @deprecated Please use controller from `@eait-playerexp-cn/authentication` package
 */
@Service()
class AuthenticateController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    protected readonly authenticate: AuthenticateCreatorWithFlaggedStatusAction,
    protected readonly findCode: FindValidRegistrationCodeAction,
    protected readonly termsAndConditions: CachedTermsAndConditions,
    protected readonly applications: InterestedCreatorApplicationsHttpClient,
    protected readonly analytics: ServerAnalytics
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const locale = this.locale(req);
    const urlLocale = this.localePathSegment(req);

    try {
      await this.tryToStartSession(req, res, locale, urlLocale);
    } catch (exception) {
      await this.tryToRegisterCreator(req, res, exception, urlLocale);
    }
  }

  private async tryToStartSession(
    req: NextApiRequestWithSession,
    res: NextApiResponse,
    locale: string,
    urlLocale: string
  ) {
    const code = this.query(req, "code");
    let creator;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      creator = await this.authenticate.executeCreatorPrograms(new AuthenticateCreatorInput(code as string));
    } else {
      creator = await this.authenticate.execute(new AuthenticateCreatorInput(code as string));
    }
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM && !creator?.isPartOfCreatorNetwork()) {
      const unknownCreator = {
        originEmail: creator.originEmail(),
        nucleusId: creator.nucleusId(),
        userName: creator.username(),
        dateOfBirth: creator.dateOfBirth()
      } as unknown as UnknownCreator;

      const interestedCreator: InitialInterestedCreator = InterestedCreatorFactory.createFrom(unknownCreator);
      const dateOfBirth = LocalizedDate.fromFormattedDate(interestedCreator?.dateOfBirth);
      if (dateOfBirth.isAfter(LocalizedDate.subtractFromNow(18, "years"))) {
        this.redirectTo(res, `${urlLocale}interested-creators/age-restriction`);
        return;
      }
      const existingApplicantInformation = await this.applications.forCreatorWithProgram(
        creator.nucleusId(),
        config.PROGRAM_CODE
      );
      if (existingApplicantInformation) {
        if (existingApplicantInformation.isAccepted()) {
          await this.addToSession(req, "nucleusId", creator.nucleusId());
          this.redirectTo(res, `${urlLocale}interested-creators/application-accepted`);
          return;
        }
        await this.handleExistingApplication(req, res, urlLocale, existingApplicantInformation, interestedCreator);
        return;
      }

      // This is a boolean after clicking on Apply button
      if (this.hasSession(req, "interestedCreator")) {
        // We need to replace the boolean with the creator information at this point
        await this.addToSession(req, "interestedCreator", interestedCreator);
        this.redirectTo(res, `${urlLocale}interested-creators/information`);
        return;
      }
      if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
      await this.addToSession(req, "noAccountCreator", interestedCreator);
      this.redirectTo(res, `${urlLocale}interested-creators/no-account`);
      return;
    }

    if (config.FLAG_CREATORS_API_WITH_PROGRAM) {
      await this.addToSession(req, "user", User.forAnalyticsWithCreatorPrograms(creator as CreatorWithCreatorPrograms));
    } else {
      await this.addToSession(req, "user", User.forAnalyticsWithFlaggedStatus(creator as CreatorWithFlaggedStatus));
    }

    await this.addToSession(req, "creatorNucleusId", creator.nucleusId());

    this.analytics.signedInToCreatorNetwork(creator);

    if (creator?.isDisabled()) {
      const interestedCreator = {
        originEmail: creator.originEmail(),
        nucleusId: creator.nucleusId(),
        defaultGamerTag: creator.username(),
        dateOfBirth: creator.dateOfBirth()
      } as InterestedCreator;
      await this.addToSession(req, "deactivatedAccount", interestedCreator);
      if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
      this.redirectTo(res, `${urlLocale}account-deactivated`);
      return;
    }

    if (creator?.isUnregistered()) {
      await this.handleIncompleteRegistration(req, res, urlLocale, creator.nucleusId());
      return;
    }

    if (creator?.isInactive()) {
      this.redirectTo(res, `${urlLocale}terms-and-conditions`);
      return;
    }

    const signedTerms = await this.termsAndConditions.signedStatusWithProgram(creator.id, locale, config.PROGRAM_CODE);
    if (!signedTerms.upToDate) {
      this.redirectTo(res, `${urlLocale}terms-and-conditions`);
      return;
    }

    if (this.hasSession(req, "initialPage")) {
      const initialPage = this.session(req, "initialPage") as string;
      await this.removeFromSession(req, "initialPage");
      this.redirectTo(res, initialPage);
      return;
    }
    if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
    this.redirectTo(res, `${urlLocale}dashboard`);
  }

  private async handleIncompleteRegistration(
    req: NextApiRequestWithSession,
    res: NextApiResponse,
    urlLocale: string,
    nucleusId: number
  ) {
    let existingApplication;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplication = await this.applications.forCreatorWithProgram(nucleusId, config.PROGRAM_CODE);
    } else if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      existingApplication = await this.applications.forCreatorWithApplicationStatus(nucleusId);
    } else {
      existingApplication = await this.applications.forCreatorWith(nucleusId);
    }
    if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
    if (existingApplication) {
      await this.addToSession(req, "creatorNucleusId", nucleusId);
      this.redirectTo(res, `${urlLocale}interested-creators/application-accepted`);
      return;
    }
    // Game changers or creators with an incomplete registration
    this.redirectTo(res, `${urlLocale}onboarding/information`);
  }

  private async tryToRegisterCreator(
    req: NextApiRequestWithSession,
    res: NextApiResponse,
    exception: Error | UnknownCreator,
    urlLocale: string
  ) {
    if (!(exception instanceof UnknownCreator)) {
      throw exception;
    }

    if (this.hasSession(req, "registrationCode")) {
      await this.handleRegistration(req, exception, res, urlLocale);
      return;
    }

    await this.handleInterestedCreator(req, res, exception, urlLocale);
  }

  private async handleRegistration(
    req: NextApiRequestWithSession,
    exception: UnknownCreator,
    res: NextApiResponse,
    urlLocale: string
  ) {
    try {
      const registrationCode = this.session(req, "registrationCode") as string;
      const input = new FindValidRegistrationCodeInput(registrationCode, exception.originEmail);
      await this.findCode.execute(input);

      await this.addToSession(req, "user", User.signUp(exception));
      const futureCreator = {
        email: exception.originEmail,
        nucleusId: exception.nucleusId,
        dateOfBirth: exception.dateOfBirth
      };
      await this.addToSession(req, "futureCreator", futureCreator as FutureCreator);
      if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
      this.redirectTo(res, `${urlLocale}onboarding/information`);
    } catch {
      if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
      this.redirectTo(res, `${urlLocale}access-error`);
    }
  }

  private async handleInterestedCreator(
    req: NextApiRequestWithSession,
    res: NextApiResponse,
    exception: UnknownCreator,
    urlLocale: string
  ) {
    const interestedCreator: InitialInterestedCreator = InterestedCreatorFactory.createFrom(exception);
    const dateOfBirth = LocalizedDate.fromFormattedDate(interestedCreator?.dateOfBirth);
    if (dateOfBirth.isAfter(LocalizedDate.subtractFromNow(18, "years"))) {
      this.redirectTo(res, `${urlLocale}interested-creators/age-restriction`);
      return;
    }
    let existingApplicantInformation;
    if (config.SEARCH_CREATORS_API_WITH_PROGRAM) {
      existingApplicantInformation = await this.applications.forCreatorWithProgram(
        exception.nucleusId,
        config.PROGRAM_CODE
      );
    } else if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      existingApplicantInformation = await this.applications.forCreatorWithApplicationStatus(exception.nucleusId);
    } else {
      existingApplicantInformation = await this.applications.forCreatorWith(exception.nucleusId);
    }
    if (existingApplicantInformation) {
      await this.handleExistingApplication(req, res, urlLocale, existingApplicantInformation, interestedCreator);
      return;
    }
    // This is a boolean after clicking on Apply button
    if (this.hasSession(req, "interestedCreator")) {
      // We need to replace the boolean with the creator information at this point
      await this.addToSession(req, "interestedCreator", interestedCreator);
      this.redirectTo(res, `${urlLocale}interested-creators/information`);
      return;
    }
    if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
    await this.addToSession(req, "noAccountCreator", interestedCreator);
    this.redirectTo(res, `${urlLocale}interested-creators/no-account`);
    return;
  }

  private async handleExistingApplication(
    req: NextApiRequestWithSession,
    res: NextApiResponse,
    urlLocale: string,
    existingApplication: InterestedCreatorApplication,
    interestedCreator
  ) {
    await this.addToSession(req, "creatorNucleusId", interestedCreator.nucleusId);
    if (config.FLAG_INITIAL_MESSAGE) await this.addToSession(req, "showInitialMessage", true);
    if (config.INTERESTED_CREATOR_REAPPLY_PERIOD) {
      await this.addToSession(req, "interestedCreator", interestedCreator);
    }
    if (existingApplication.isPending()) {
      this.redirectTo(res, `${urlLocale}interested-creators/application-pending`);
      return;
    }
    this.redirectTo(res, `${urlLocale}interested-creators/application-rejected`);
  }
}

export default AuthenticateController;
