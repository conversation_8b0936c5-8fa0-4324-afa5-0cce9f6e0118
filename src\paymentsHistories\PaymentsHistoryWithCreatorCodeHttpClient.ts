import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import PaymentsHistoryWithCreatorCode from "./PaymentsHistoryWithCreatorCode";
import { PaymentsCriteria } from "../api/services/PaymentsService";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
export default class PaymentsHistoryWithCreatorCodeHttpClient {
  constructor(@Inject("paymentClient") private client: TraceableHttpClient) {}

  async matching(id: string, criteria: PaymentsCriteria): Promise<PaymentsHistoryWithCreatorCode> {
    const response = (await this.client.get(`/v2/creators/${id}/payment-history`, {
      query: criteria
    })) as AxiosResponse;
    return Promise.resolve(response.data as PaymentsHistoryWithCreatorCode);
  }
}
