import React from "react";
import { AgeRestrictionPageLabels } from "@src/contentManagement/AgeRestrictionPageMapper";
import AgeRestriction from "../../../pages/interested-creators/age-restriction";
import { render, screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { CommonPageLabels } from "@src/contentManagement/CommonPageMapper";

jest.mock("next/router");
jest.mock("next/dynamic", () => () => {
  const DynamicComponent = () => <div data-testid="dynamic" />;
  DynamicComponent.displayName = "DynamicComponent";
  return DynamicComponent;
});

describe("AgeRestriction", () => {
  const mockPush = jest.fn().mockResolvedValue(true);
  const router = {
    locale: "en-us",
    push: mockPush
  };

  const baseAgeRestrictionProps = {
    pageLabels: {
      ageRestrictionLabels: {
        pageTitle: "Age Restriction",
        title: "Age Verification Required",
        description: "You must be 18 or older"
      },
      commonPageLabels: {
        creatorNetwork: "Creator Network",
        close: "Close"
      }
    } as unknown as AgeRestrictionPageLabels & CommonPageLabels,
    ageRestrictionBannerImage: "/img/home-header--980w-x-690h.png"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("shows remote age restriction component", async () => {
    render(<AgeRestriction {...baseAgeRestrictionProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with different banner image", async () => {
    const propsWithDifferentImage = {
      ...baseAgeRestrictionProps,
      ageRestrictionBannerImage: "/custom-banner.png"
    };

    render(<AgeRestriction {...propsWithDifferentImage} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with different locale", async () => {
    const routerWithDifferentLocale = {
      ...router,
      locale: "fr-fr"
    };
    (useRouter as jest.Mock).mockImplementation(() => routerWithDifferentLocale);

    render(<AgeRestriction {...baseAgeRestrictionProps} />);

    expect(await screen.findByTestId("dynamic")).toBeInTheDocument();
  });

  it("shows component with minimal page labels", () => {
    const propsWithMinimalLabels = {
      ...baseAgeRestrictionProps,
      pageLabels: {
        ageRestrictionLabels: {
          pageTitle: "Age Check"
        },
        commonPageLabels: {
          creatorNetwork: "Network",
          close: "Close"
        }
      } as unknown as AgeRestrictionPageLabels & CommonPageLabels
    };

    expect(() => render(<AgeRestriction {...propsWithMinimalLabels} />)).not.toThrow();
  });

  it("shows header with correct labels", async () => {
    render(<AgeRestriction {...baseAgeRestrictionProps} />);

    expect(screen.getByText("Creator Network")).toBeInTheDocument();
  });
});
