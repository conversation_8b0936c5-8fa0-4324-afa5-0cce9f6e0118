.header-container {
  @apply flex flex-col;
}
.header-web {
  @apply fixed left-meas0 top-meas20 z-[50]  h-meas30 w-full bg-white px-meas12  md:px-meas24 xl:block;
  transition: top 0.35s ease-in-out;
}
.header-mobile {
  @apply fixed left-meas0 top-meas20 z-30 block h-meas30 w-full bg-white xl:hidden;
  transition: top 0.35s ease-in-out;
}
.header-mobile-nav {
  @apply flex items-center;
}
.ea-icon-header-mobile path {
  @apply fill-indigo-40;
}
.header-web .header-web-container {
  @apply relative flex h-full justify-between;
}
.ea-icon-header-web {
  @apply mr-[9.5px];
}
.header-web-ea-title-container {
  @apply flex cursor-pointer items-center;
}
.header-mobile-container-open {
  @applybg-gray-10;
}
.header-web-nav-col {
  @apply mr-meas12 flex;
}
.header-web .title {
  @apply mr-meas18 font-display-bold xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.header-web .navlink {
  @apply mr-meas20 flex cursor-pointer;
}
.header-web .current-link {
  @apply border-b-4 border-indigo-40;
}
.header-mobile .header-mobile-container {
  @apply flex h-full flex-row-reverse justify-between px-meas12 py-meas6;
}
.header-mobile .menu-expand {
  @apply absolute z-10 w-full bg-white pb-meas2 pt-meas2 transition-all;
}
.header-mobile .navlink {
  @apply flex cursor-pointer items-center pb-meas9 pt-meas9;
}
.navlink-icon-mobile {
  @apply ml-meas7 mr-meas6;
}
.nav-web-link-label {
  @apply mt-auto self-center border-b-4 border-white pb-meas8 font-text-regular font-bold xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.nav-mobile-link-label {
  @apply font-text-regular font-bold xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.ea-header-web-profile-image-container:hover {
  @apply focus:outline-none;
}
.ea-header-web-profile-image-container-focus {
  @apply focus:outline-none;
}
.ea-header-web-profile-image {
  @apply h-meas20 cursor-pointer rounded-[20px];
}
.ea-header-web-profile-menu {
  @apply absolute right-meas2 top-meas24 z-10 w-[206px] rounded-b bg-white xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
}
.ea-header-web-profile-menu-hide {
  @apply hidden;
}
.ea-header-web-profile-menu-link {
  @apply flex w-full cursor-pointer items-center gap-meas2 px-meas4 py-meas6 hover:bg-gray-10;
}
.ea-header-web-profile-menu-link-icon {
  @apply mr-meas2 mt-[3px];
}
.ea-header-web-profile-menu-link-label {
  @apply font-text-regular text-gray-90;
}

.header-web-container .select-header {
  @apply border-none;
}

.header-web-container .select-header {
  @apply border-none;
}
.header-web-container .select-header-title {
  @apply w-auto;
}
.navlink-open {
  @apply bg-[#F2F2F2] pb-meas0 pt-meas0;
}
.header-mobile-about-drop {
  @apply mt-[-4px] flex flex-col pb-meas4 pl-[72px];
}
.header-mobile-about-drop-nav-label {
  @apply mb-meas6 cursor-pointer text-left font-text-regular font-bold xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.header-web-container .remove-mr {
  @apply mr-meas0;
}

.header-mobile .navlink:focus-within {
  @apply bg-indigo-40 text-white;
}

.header-mobile .navlink:focus-within svg > path {
  @apply fill-[#FFFFFF];
}

.header-container .sticky {
  @apply fixed top-meas0 z-[300] w-[100%];
  transition: top 0.35s ease-in-out;
}

.header-mobile .header-mobile-container-open {
  @apply flex-row transition-all;
}

.header-web-select {
  @apply flex items-center;
}

.header-web-action-btn-col {
  @apply flex items-center;
}

.header-container
  + div
  > .opportunity-perks-container
  .opportunity-perks-full-screen
  .opportunity-perks-header-container-wrapper,
.header-container
  + div
  > .opportunity-perks-container
  .opportunity-perks-full-screen
  .opportunity-perks-header-mobile-or-tab-container,
.header-container
  + div
  > .opportunity-perks-container
  .opportunity-perks-full-screen
  .opportunity-perks-header-container,
.header-container + div > .disclosure-wrapper,
.header-container + div > .trust-and-safety-guidelines,
.header-container + div.profile-base,
.header-container + div > .reward-hero-container,
.header-container + div > .rewards-page .reward-hero-container,
.header-container + div > .faqs-header-container,
.header-container + div.dashboard-container,
.header-container + div.payment-information-container,
.header-container + div > .my-content-container,
.header-container + div.opportunities-container,
.header-container + div > section {
  @apply pt-[6.25rem];
  transition: all 0.35s ease-in-out;
}
.header-topnav-parent-container {
  @apply fixed z-[99] mt-meas20 w-full lg:h-auto;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-child-container {
  @apply relative;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-child-container
  > .notifications-bell-container::after {
  @apply absolute bottom-[-3px] right-[-14px] h-meas2 w-meas26 bg-white opacity-0 transition-opacity duration-200;
  content: "";
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  .notification-dropdown.show {
  @apply md:fixed lg:absolute;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-child-container
  > .profile-avatar-container::after {
  @apply absolute bottom-[-8px] right-[-7px] h-meas2 w-meas26 bg-white opacity-0 transition-opacity duration-200;
  content: "";
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-active-child
  > .profile-avatar-container::after,
.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > .topnav-icon-container
  > .topnav-active-child
  > .notifications-bell-container::after {
  @apply opacity-100;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > div
  > .mobile-menu-expand
  > .sidenav-mobile-container
  > .topnav-mobile-active-child {
  @apply mr-meas8 rounded-bl-none rounded-br-[4px] rounded-tl-none rounded-tr-[4px] border-l-[8px] border-solid border-l-[#429CF9] bg-custom-7;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > div
  > .mobile-menu-expand
  > .sidenav-mobile-section-top {
  height: 47%;
}

.header-topnav-parent-container > .topnav-web-container > .topnav-parent-container > div > .mobile-menu-expand {
  @apply h-screen;
}

.header-topnav-parent-container
  > .topnav-web-container
  > .topnav-parent-container
  > div
  > .mobile-menu-expand
  > .sidenav-mobile-container
  > .topnav-mobile-active-child
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container {
  @apply ml-meas2;
}
