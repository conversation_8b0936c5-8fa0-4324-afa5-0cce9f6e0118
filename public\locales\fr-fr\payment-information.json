{"paymentSettings": "Paramètres de paiement", "invoicesHistory": "Historique des factures", "paymentsHistory": "Historique des paiements", "paymentSettingsDescription": "Maintenez vos informations de paiement à jour. Nous en avons besoin pour vous payer les opportunités rémunérées.", "paymentInvoicesDescription": "Consultez l’historique de vos factures ci-dessous.", "paymentHistoryDescription": "Consultez l’historique de vos paiements ci-dessous.", "pageTitle": "Informations de paiement", "payableStatus": "À payer", "nonPayableStatus": "Détails du paiement incomplets", "nonPayableStatusHelp": "V<PERSON> devez fournir des informations sur le paiement pour obtenir une rémunération.", "nonPayableStatusClickHere": "Cliquez ici", "nonPayableStatusStart": "pour commencer.", "yourPayments": "Vos paiements", "paymentInformationBannerWarning": "Veuillez fournir vos informations de paiement", "paymentInformationBannerDescription": "Pour recevoir le paiement de vos opportunités, vous devez fournir vos informations de paiement.", "paymentInformationBannerDescription_2": "Cliquez sur l’onglet Paramètres de paiement ci-dessus ou ", "paymentInformationClick": "cliquez ici", "paymentInformationBannerDescription_3": " pour commencer", "paymentOverview": "<PERSON><PERSON><PERSON><PERSON>", "paymentTotalPaid": "Montant total payé (en USD)", "paymentPendingPayments": "Paiements en attente (en USD)", "transactionHistory": "Historique des transactions", "transactionHistoryDetails": "En fonction des filtres sélectionnés, vous pourrez consulter les détails des transactions sur une période maximale de 24 mois.", "paymentGridDescription": "Description", "paymentGridType": "Type", "paymentGridStatus": "Statut", "paymentGridAmountDue": "<PERSON><PERSON> (en USD)", "paymentGridDate": "Date", "paymentGridDateHelp": "Comptez plusieurs jours pour le traitement de votre transaction, suivant votre mode de paiement privilégié.", "paymentGridContract": "Contrat", "statusPending": "En attente", "statusProcessed": "Traitement effectué", "downloadContract": "Télécharger le contrat", "typeCreatorCode": "Code de création", "typeOpportunity": "Opportunité", "noPayments": "Aucun paiement", "noPaymentsDescription": "Vous n’avez terminé aucune ", "noPaymentsLink": "opportunité rémunérée", "downloadContractLabel": "Téléchargement du contrat", "opportunityImageLabel": "Image d’opportunité", "filteredBy": "Filtrer par :", "noProcessedPayments": "Aucun paiement traité", "noProcessedPaymentsDescription": "Vous n'avez aucun paiement ayant é<PERSON> traité", "noPendingPayments": "Aucun paiement en attente", "noPendingPaymentsDescription": "Vous n'avez aucun paiement en attente", "opportunityTitle": "Support-a-Creator", "simsMakerProgram": "Programme Sims Maker"}