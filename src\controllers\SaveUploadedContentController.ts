import { NextApiResponse } from "next";
import SubmittedContentHttpClient, { UploadedFileContent } from "../submittedContent/SubmittedContentHttpClient";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class SaveUploadedContentController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly submittedContent: SubmittedContentHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const { participationId, fileName, title, versionId, thumbnail, id, contentType } = req.body;

    await this.submittedContent.saveUploadedContent(participationId, {
      fileName,
      title,
      versionId,
      thumbnail,
      id,
      contentType
    } as UploadedFileContent);

    this.empty(res, HttpStatus.OK);
  }
}

export default SaveUploadedContentController;
