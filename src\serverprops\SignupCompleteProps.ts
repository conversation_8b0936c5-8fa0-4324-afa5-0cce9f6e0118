import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import SignupCompletePagePropsController from "./controllers/SignupCompletePagePropsController";

const signupCompleteProps = (locale: string) =>
  serverPropsControllerFactory(new SignupCompletePagePropsController(ApiContainer.get("options"), locale));

export default signupCompleteProps;
