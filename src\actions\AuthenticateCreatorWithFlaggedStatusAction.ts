import CreatorsHttpClient from "@src/creators/CreatorsHttpClient";
import AuthenticateCreatorAction from "./AuthenticateCreatorAction";
import AuthenticationProvider from "@src/identity/AuthenticationProvider";
import CreatorsWithProgramsHttpClient from "@src/creators/CreatorsWithProgramsHttpClient";

/**
 * @deprecated
 */
class AuthenticateCreatorWithFlaggedStatusAction extends AuthenticateCreatorAction {
  constructor(
    provider: AuthenticationProvider,
    creators: CreatorsHttpClient,
    redirectUrl: URL,
    creatorsWithProgramHttpClient: CreatorsWithProgramsHttpClient
  ) {
    super(provider, creators, redirectUrl, creatorsWithProgramHttpClient);
  }
}

export default AuthenticateCreatorWithFlaggedStatusAction;
