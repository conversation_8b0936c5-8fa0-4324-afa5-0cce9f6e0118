import "reflect-metadata";
import OpportunitiesRewardsHero from "../components/rewards/OpportunitiesRewardsHero";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useEffect, useMemo } from "react";
import labelsCommon from "../config/translations/common";
import labelsRewards from "../config/translations/opportunities-rewards";
import withUserSession from "../src/utils/WithUserSession";
import BrowserAnalytics from "../src/analytics/BrowserAnalytics";
import flags from "../utils/feature-flags";
import { AuthenticatedUserFactory } from "../src/analytics/BrowserAnalytics";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Footer from "../components/footer/ProgramFooter";
import Header from "../components/header/Header";
import { mapNotificationsBellLabels } from "../config/translations/mappers/notifications";
import runtimeConfiguration from "../src/configuration/runtimeConfiguration";
import featureFlags from "../utils/feature-flags";
import { createRouter } from "next-connect";
import initializeSession from "../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import errorLogger from "../src/serverprops/middleware/ErrorLogger";
import opportunityRewardsProps from "../src/serverprops/OpportunityRewardsProps";

export default function OpportunitiesRewards({
  user,
  interestedCreator,
  analytics = new BrowserAnalytics(),
  FLAG_NEW_FOOTER_ENABLED,
  FLAG_NEW_NAVIGATION_ENABLED
}) {
  const router = useRouter();
  const { t } = useTranslation([
    "common",
    "opportunities-rewards",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { hero, layout, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      ...labelsRewards(t),
      layout: labelsCommon(t),
      character: "/img/rewards/reward-hero.png",
      notificationsLabels: notificationBellLabels
    };
    labels.layout.footer = { locale: router.locale, labels: labels.layout.footer };
    return labels;
  }, [t]);
  const headerLabels = { labels: { ...layout.header, ...layout.buttons } };
  const footerLabels = layout.footer.labels;

  useEffect(() => {
    if (user) {
      analytics.viewedMarketingPage({ locale: router.locale, page: router.pathname });
    }
  }, []);

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.perks}>
        <Header
          {...headerLabels}
          user={user}
          notificationsLabels={notificationsLabels}
          analytics={analytics}
          interestedCreator={interestedCreator}
          FLAG_NEW_NAVIGATION_ENABLED={FLAG_NEW_NAVIGATION_ENABLED}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={FLAG_NEW_NAVIGATION_ENABLED && !!user}>
        <OpportunitiesRewardsHero
          opportunityRewardsLabels={hero}
          isLoggedIn={!!user}
          interestedCreator={interestedCreator}
          applyNow={layout.header.applyNow}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          FLAG_NEW_FOOTER_ENABLED={FLAG_NEW_FOOTER_ENABLED}
          locale={router.locale}
          labels={footerLabels}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ locale, req, res }) => {
  if (featureFlags.isPerProgramProfileEnabled()) {
    const router = createRouter();
    router
      .use(errorLogger)
      .use(initializeSession)
      .use(addIdentityTelemetryAttributes)
      .get(opportunityRewardsProps(locale));

    return await router.run(req, res);
  }

  const user = await withUserSession(req, res);
  const authenticatedUser = user
    ? AuthenticatedUserFactory.fromSession(user, featureFlags.isCreatorsAPIWithProgram())
    : null;

  return {
    props: {
      runtimeConfiguration: runtimeConfiguration(authenticatedUser),
      user: authenticatedUser,
      interestedCreator: flags.isInterestedCreatorFlowEnabled(),
      ...(await serverSideTranslations(locale, [
        "common",
        "opportunities-rewards",
        "notifications",
        "connect-accounts",
        "opportunities"
      ])),
      FLAG_NEW_FOOTER_ENABLED: flags.isNewFooterEnabled(),
      FLAG_NEW_NAVIGATION_ENABLED: flags.isNewNavigationEnabled()
    }
  };
};
