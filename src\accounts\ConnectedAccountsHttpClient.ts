import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import ConnectedAccount from "../channels/ConnectedAccount";
import ConnectedAccountCredentials from "../channels/ConnectedAccountCredentials";
import FacebookPageCredentials from "../channels/facebook/FacebookPageCredentials";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type FacebookPage = {
  id: string;
  accessToken: string;
  name: string;
};
export type FacebookPages = {
  pages: FacebookPage[];
};

@Service()
class ConnectedAccountsHttpClient {
  constructor(@Inject("contentSubmissionClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/viewInterestedCreatorConnectedAccounts}
   */
  async getAllConnectedAccounts(nucleusId: number): Promise<Array<ConnectedAccount>> {
    const response = (await this.client.get(`/v1/connected-accounts/${nucleusId}`)) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Social-Channels/operation/viewInterestedCreatorConnectedAccountsV2}
   */
  async getAllConnectedAccountsWithExpirationStatus(nucleusId: number): Promise<Array<ConnectedAccount>> {
    const response = (await this.client.get(`/v2/connected-accounts/${nucleusId}`)) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveYoutubeAccount}
   */
  async connectYouTubeAccount(credentials: ConnectedAccountCredentials): Promise<void> {
    await this.client.post("/v1/youtube-accounts", { body: credentials });
    return Promise.resolve();
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveTwitchAccount}
   */
  async connectTwitchAccount(credentials: ConnectedAccountCredentials): Promise<void> {
    await this.client.post("/v1/twitch-accounts", { body: credentials });
    return Promise.resolve();
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/viewFacebookPages}
   */
  async facebookPages(code: string): Promise<FacebookPages> {
    const response = (await this.client.get("/v1/facebook-pages", { query: { code } })) as AxiosResponse;
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveFacebookAccount}
   */
  async connectFacebookPage(credentials: FacebookPageCredentials): Promise<void> {
    await this.client.post("/v1/facebook-accounts", { body: credentials });
    return Promise.resolve();
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveInstagramAccount}
   */
  async connectInstagramAccount(credentials: ConnectedAccountCredentials): Promise<void> {
    await this.client.post("/v1/instagram-accounts", { body: credentials });
    return Promise.resolve();
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveTikTokAccountV2}
   */
  async connectTikTokAccount(credentials: ConnectedAccountCredentials): Promise<void> {
    await this.client.post("/v2/tiktok-accounts", { body: credentials });
    return Promise.resolve();
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/disconnectAccount}
   */
  async removeConnectedAccount(id: string, type: string): Promise<void> {
    await this.client.delete(`/v1/connected-accounts/${id}`, { query: { type } });
    return Promise.resolve();
  }
}

export default ConnectedAccountsHttpClient;
