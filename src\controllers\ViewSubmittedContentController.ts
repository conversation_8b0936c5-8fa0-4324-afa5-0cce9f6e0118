import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import SubmittedContentHttpClient, {
  SubmittedContentCriteriaWithProgramCode
} from "../submittedContent/SubmittedContentHttpClient";
import User from "@src/authentication/User";
import config from "config";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ViewSubmittedContentController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly contents: SubmittedContentHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const criteria = req.query as unknown as SubmittedContentCriteriaWithProgramCode;
    criteria.creatorId = !criteria.participationId
      ? config.FLAG_PER_PROGRAM_PROFILE
        ? this.identity(req).id
        : (this.session(req, "user") as User).id
      : undefined;

    if (criteria.creatorId) {
      criteria.participationId = criteria.deliverableId = undefined;
    }

    // Use feature flag to determine which API method to call
    let submittedContents;
    if (config.FLAG_SUBMITTED_CONTENT_WITH_PROGRAM) {
      criteria.program = config.PROGRAM_CODE;
      submittedContents = await this.contents.getSubmittedContentsWithProgramCode(criteria);
    } else if (config.FLAG_CONTENT_WITH_FINAL_REMARK) {
      submittedContents = await this.contents.getSubmittedContentsFinalRemarks(criteria);
    } else {
      submittedContents = await this.contents.getSubmittedContents(criteria);
    }
    this.json(res, submittedContents);
  }
}

export default ViewSubmittedContentController;
