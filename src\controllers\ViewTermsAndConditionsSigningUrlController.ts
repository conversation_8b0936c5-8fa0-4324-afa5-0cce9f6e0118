import { NextApiResponse } from "next";
import TermsAndConditionsHttpClient from "../pactSafe/TermsAndConditionsHttpClient";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewTermsAndConditionsSigningUrlController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly termsAndConditions: TermsAndConditionsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const signingUrlResponse = await this.termsAndConditions.signerUrlWithTier(req.body);
    this.json(res, signingUrlResponse);
  }
}

export default ViewTermsAndConditionsSigningUrlController;
