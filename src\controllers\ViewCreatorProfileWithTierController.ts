import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import User from "../authentication/User";
import CreatorsWithTierHttpClient from "../creators/CreatorsWithTierHttpClient";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "../../config";

@Service()
class ViewCreatorProfileWithTierController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly creators: CreatorsWithTierHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
    const creator = await this.creators.withId(user.id);

    // Only add to session if it's a User object
    if (!config.FLAG_PER_PROGRAM_PROFILE) {
      await this.addToSession(req, "user", User.from(user as User));
    }

    this.json(res, creator);
  }
}

export default ViewCreatorProfileWithTierController;
