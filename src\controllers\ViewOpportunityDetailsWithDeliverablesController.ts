import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import CreatorsWithFlaggedStatusHttpClient from "../creators/CreatorsWithFlaggedStatusHttpClient";
import CreatorWithFlaggedStatus from "../creators/CreatorWithFlaggedStatus";
import OpportunityWithDeliverables from "@src/opportunities/OpportunityWithDeliverables";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import LegacyOpportunitiesHttpClient from "@src/opportunities/LegacyOpportunitiesHttpClient";
import config from "config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { CreatorResponse } from "@eait-playerexp-cn/creator-types";

@Service()
class ViewOpportunityDetailsWithDeliverablesController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly opportunities: LegacyOpportunitiesHttpClient,
    private readonly creators: CreatorsWithFlaggedStatusHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const opportunityId = req.query.id;
    const user = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);

    const [opportunities, creators]: [
      opportunities: OpportunityWithDeliverables,
      creators: CreatorWithFlaggedStatus | CreatorResponse
    ] = await Promise.all([
      this.opportunities.withOpportunityDeliverables(opportunityId as string, user.id as string),
      config.FLAG_PER_PROGRAM_PROFILE ? this.creators.withIdForProgramProfile(user.id) : this.creators.withId(user.id)
    ]);

    const resp = {
      opportunity: opportunities,
      creator: creators
    };

    this.json(res, resp);
  }
}

export default ViewOpportunityDetailsWithDeliverablesController;
