import React, { useState } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import UploadContentSubmissionModal, {
  UploadContentSubmissionModalProps
} from "../components/pages/content-submission/UploadContentSubmissionModal";
import Form from "../components/Form";

const meta: Meta<typeof UploadContentSubmissionModal> = {
  title: "Creator Network/Pages/Content Submission/Upload Content Submission Modal",
  component: UploadContentSubmissionModal
};

export default meta;

type Story = StoryObj<typeof UploadContentSubmissionModal>;

const render = (args: UploadContentSubmissionModalProps) => {
  const [show, setShow] = useState(false);
  const showModal = () => {
    setShow(true);
  };
  const closeModal = () => {
    setShow(false);
  };
  const submitHandler = () => {};
  return (
    <div>
      <button className="btn btn-primary" onClick={showModal}>
        Show
      </button>
      <Form onSubmit={submitHandler} defaultValues={{}}>
        {show ? <UploadContentSubmissionModal {...args} onClose={closeModal} /> : ""}
      </Form>
    </div>
  );
};

export const UploadContent: Story = {
  args: {
    title: "Upload a file from your device",
    formLabels: {
      contentTitle: "Content Title",
      contentTitlePlaceholder: "Add Content Title",
      contentType: "Content Type",
      fileUpload: "File Upload",
      chooseFile: "Choose File",
      noFileChoosen: "No file chosen",
      acceptedFormats: "Accepted File Formats",
      maxFileSize: "Max File Size",
      fileSelected: "File selected",
      fileUploading: "File uploading...",
      removeSelectedFile: "Remove selected file",
      uploadFileProgress: "23%"
    },
    contentTypes: [
      { name: "", label: "Please select", value: "" },
      { name: "VIDEO", label: "Video", value: "VIDEO" },
      { name: "IMAGE", label: "Image", value: "IMAGE" },
      { name: "AUDIO", label: "Audio", value: "AUDIO" },
      { name: "TEXT", label: "Text", value: "TEXT" }
    ],
    buttonsLabels: {
      cancel: "Cancel",
      upload: "Upload",
      close: "Close"
    },
    contentSubmissionLabels: {
      contentTitleRequired: "Content Title is required",
      contentTypeRequired: "Content Type is required",
      contentTitleLongMessage: "Content title",
      maxLimitMessage: "Maximum file size",
      invalidFileTypeMessage: "Invalid file type selected",
      success: {
        title: "File uploaded successfully",
        content: "Image file"
      },
      error: {
        title: "File Upload Failed",
        content: "There was an error uploading your file. Please try again."
      }
    },
    fileTypes: []
  },
  render
};
