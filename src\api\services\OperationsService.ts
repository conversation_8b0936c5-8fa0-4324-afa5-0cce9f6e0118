import { AxiosResponse } from "axios";
import { OpportunityWithPerks } from "./OpportunityService";
import { NextRouter } from "next/router";
import client from "./Client";
import AssignedGameCodeWithStatus from "@src/opportunities/AssignedGameCodeWithStatus";
import JoinOpportunityInput from "@src/actions/JoinOpportunity/JoinOpportunityInput";
import Participation from "@src/opportunities/Participation";

const viewAssignedGameCode = async (id: string): Promise<AxiosResponse<AssignedGameCodeWithStatus[]>> => {
  return (await client.get(`/api/participations/${id}/game-codes`)) as AxiosResponse<AssignedGameCodeWithStatus[]>;
};

const saveParticipation = async (
  participation: JoinOpportunityInput,
  opportunity: OpportunityWithPerks
): Promise<AxiosResponse<Participation>> => {
  return (await client.post(`/api/opportunities/${opportunity.id}/participations`, {
    body: participation
  })) as AxiosResponse<Participation>;
};

const navigateToJoinOpportunityNextSteps = (opportunity: OpportunityWithPerks, router: NextRouter): void => {
  if (router.pathname === "/opportunities/[id]/registrations") {
    if (opportunity.hasGameCodes && (router.query.step === "criteria" || router.query.step === undefined)) {
      router.push(`/opportunities/${opportunity.id}/registrations?step=game-code`);
    } else if (
      opportunity.hasDeliverables &&
      (router.query.step === "criteria" || router.query.step === undefined || router.query.step === "game-code")
    ) {
      router.push(`/opportunities/${opportunity.id}/registrations?step=content-guidelines`);
    } else {
      router.push(`/opportunities/${opportunity.id}/registrations?step=thanks`);
    }
  }
};

const viewAssignedGameCodes = async (
  participationIds: Array<string>
): Promise<AxiosResponse<AssignedGameCodeWithStatus>> => {
  return (await client.post("/api/assigned-game-codes", {
    body: participationIds
  })) as AxiosResponse<AssignedGameCodeWithStatus>;
};

const claimGameCode = async (participationId: string): Promise<AxiosResponse<void>> => {
  return (await client.put("/api/claimed-game-codes", { body: { participationId } })) as AxiosResponse<void>;
};

const OperationsService = {
  saveParticipation,
  navigateToJoinOpportunityNextSteps,
  viewAssignedGameCode,
  viewAssignedGameCodes,
  claimGameCode
};

export default OperationsService;
