import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { RequestHandlerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { SignupCompleteProps } from "pages/signup-complete";

export default class SignupCompletePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<SignupCompleteProps>
{
  constructor(options: RequestHandlerOptions, private readonly currentLocale: string) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<SignupCompleteProps>> {
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        ...(await serverSideTranslations(this.currentLocale, ["common", "signup-complete"]))
      }
    };
  }
}
