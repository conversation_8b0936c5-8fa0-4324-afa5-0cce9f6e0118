import { Inject, Service } from "typedi";
import SignerInformation from "./SignerInformation";
import SignedStatus from "./SignedStatus";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type SigningUrl = {
  contractUrl: string;
};

@Service()
export default class TermsAndConditionsHttpClient {
  constructor(@Inject("legalClient") private readonly client: TraceableHttpClient) {}

  /**
   * @deprecated
   */
  async signedStatus(creatorId: string, locale: string): Promise<SignedStatus> {
    const res = await this.client.get(`/v1/terms-and-conditions/acceptance-status/${locale}/${creatorId}`);
    return Promise.resolve(res.data as SignedStatus);
  }

  async signedStatusWithProgram(creatorId: string, locale: string, program: string): Promise<SignedStatus> {
    const res = await this.client.get(`/v2/terms-and-conditions-status/${creatorId}`, { query: { locale, program } });
    return Promise.resolve(res.data as SignedStatus);
  }

  /**
   * @deprecated
   */
  async signerUrl(signer: SignerInformation): Promise<Record<string, unknown>> {
    const res = await this.client.post("/v1/terms-and-conditions/signing-url", { body: signer });
    return Promise.resolve(res.data);
  }

  async signerUrlWithTier(signer: SignerInformation): Promise<SigningUrl> {
    const res = await this.client.post("/v2/terms-and-conditions/signing-url", { body: signer });
    return Promise.resolve(res.data);
  }
}
