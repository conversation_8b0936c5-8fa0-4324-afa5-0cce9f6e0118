import { NextApiResponse } from "next";
import * as cookie from "cookie";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";

/**
 * @deprecated
 */
export default function saveLocaleCookie(req: NextApiRequestWithSession, res: NextApiResponse, locale: string): void {
  if (locale !== req.cookies.NEXT_LOCALE) {
    res.setHeader(
      "Set-Cookie",
      cookie.serialize("NEXT_LOCALE", locale, {
        expires: new Date("9999-12-31T23:59:59Z"),
        path: "/"
      })
    );
  }
}
