import { NextApiResponse } from "next";
import SubmittedContentHttpClient, {
  UploadedFileContentDeliverable
} from "../submittedContent/SubmittedContentHttpClient";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class SaveUploadContentDeliverableController extends RequestHand<PERSON> implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly submittedContent: SubmittedContentHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const { participationId, fileName, title, versionId, thumbnail, id, contentType, deliverableId } = req.body;

    await this.submittedContent.submitUploadedContentDeliverable(participationId, {
      fileName,
      title,
      versionId,
      thumbnail,
      id,
      contentType,
      deliverableId
    } as UploadedFileContentDeliverable);

    this.empty(res, HttpStatus.OK);
  }
}

export default SaveUploadContentDeliverableController;
